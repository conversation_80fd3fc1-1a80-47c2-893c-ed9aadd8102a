-- Insert a dummy company with all fields populated
-- This query handles all the subscription-related fields and enums properly

INSERT INTO "Company" (
    "id",
    "address",
    "city",
    "country",
    "name",
    "latitude",
    "longitude",
    "detailContent",
    "foundingYear",
    "headerImageUrl",
    "logoImageUrl",
    "totalEmployees",
    "dynamicLink",
    "stripeCustomerId",
    "isFairManaged",
    "contactEmail",
    "hasActiveSubscription",
    "subscriptionExpiresAt",
    "subscriptionStatus",
    "current_plan_id",
    "subscriptionStartDate",
    "subscriptionEndDate",
    "createdAt",
    "updatedAt",
    "company_user_id"
) VALUES (
    gen_random_uuid(), -- id
    '123 Main Street', -- address
    'Berlin', -- city
    'Germany', -- country
    'Dummy Tech Company GmbH', -- name
    52.5200, -- latitude (Berlin)
    13.4050, -- longitude (Berlin)
    'We are a leading technology company specializing in innovative solutions for businesses worldwide. Our team is passionate about creating cutting-edge software that transforms industries.', -- detailContent
    2015, -- foundingYear
    'https://example.com/header-image.jpg', -- headerImageUrl
    'https://example.com/logo.png', -- logoImageUrl
    150, -- totalEmployees
    'https://example.com/company/dummy-tech', -- dynamicLink
    NULL, -- stripeCustomerId (will be created when they subscribe)
    true, -- isFairManaged
    '<EMAIL>', -- contactEmail
    false, -- hasActiveSubscription
    NULL, -- subscriptionExpiresAt
    'PENDING'::"SubscriptionStatus", -- subscriptionStatus (using enum cast)
    NULL, -- current_plan_id (no plan selected yet)
    NULL, -- subscriptionStartDate
    NULL, -- subscriptionEndDate
    NOW(), -- createdAt
    NOW(), -- updatedAt
    NULL -- company_user_id (will be linked when user creates company)
);

-- Alternative with an active subscription
INSERT INTO "Company" (
    "id",
    "address",
    "city", 
    "country",
    "name",
    "latitude",
    "longitude",
    "detailContent",
    "foundingYear",
    "headerImageUrl",
    "logoImageUrl",
    "totalEmployees",
    "dynamicLink",
    "stripeCustomerId",
    "isFairManaged",
    "contactEmail",
    "hasActiveSubscription",
    "subscriptionExpiresAt",
    "subscriptionStatus",
    "current_plan_id",
    "subscriptionStartDate",
    "subscriptionEndDate",
    "createdAt",
    "updatedAt",
    "company_user_id"
) VALUES (
    gen_random_uuid(), -- id
    '456 Innovation Avenue', -- address
    'Munich', -- city
    'Germany', -- country
    'Premium Solutions AG', -- name
    48.1351, -- latitude (Munich)
    11.5820, -- longitude (Munich)
    'Premium Solutions AG is a premier consulting firm providing enterprise-grade solutions to Fortune 500 companies. We specialize in digital transformation and cloud architecture.', -- detailContent
    2010, -- foundingYear
    'https://example.com/premium-header.jpg', -- headerImageUrl
    'https://example.com/premium-logo.png', -- logoImageUrl
    500, -- totalEmployees
    'https://example.com/company/premium-solutions', -- dynamicLink
    'cus_TestStripeCustomer123', -- stripeCustomerId
    true, -- isFairManaged
    '<EMAIL>', -- contactEmail
    true, -- hasActiveSubscription
    NOW() + INTERVAL '30 days', -- subscriptionExpiresAt (30 days from now)
    'ACTIVE'::"SubscriptionStatus", -- subscriptionStatus (active subscription)
    NULL, -- current_plan_id (would need to reference an actual PricingPlan id)
    NOW(), -- subscriptionStartDate
    NOW() + INTERVAL '30 days', -- subscriptionEndDate
    NOW() - INTERVAL '90 days', -- createdAt (company created 90 days ago)
    NOW(), -- updatedAt
    NULL -- company_user_id
);

-- Example with all subscription statuses (for testing different states)
-- PENDING status
INSERT INTO "Company" (
    "id", "address", "city", "country", "name", 
    "subscriptionStatus", "hasActiveSubscription",
    "createdAt", "updatedAt"
) VALUES (
    gen_random_uuid(),
    '789 Startup Lane', 'Hamburg', 'Germany', 'Startup Pending GmbH',
    'PENDING'::"SubscriptionStatus", false,
    NOW(), NOW()
);

-- TRIAL status
INSERT INTO "Company" (
    "id", "address", "city", "country", "name",
    "subscriptionStatus", "hasActiveSubscription",
    "subscriptionStartDate", "subscriptionEndDate",
    "createdAt", "updatedAt"
) VALUES (
    gen_random_uuid(),
    '321 Trial Street', 'Frankfurt', 'Germany', 'Trial Company GmbH',
    'TRIAL'::"SubscriptionStatus", true,
    NOW(), NOW() + INTERVAL '14 days',
    NOW(), NOW()
);

-- ACTIVE status
INSERT INTO "Company" (
    "id", "address", "city", "country", "name",
    "subscriptionStatus", "hasActiveSubscription",
    "subscriptionStartDate", "subscriptionEndDate",
    "createdAt", "updatedAt"
) VALUES (
    gen_random_uuid(),
    '654 Active Road', 'Cologne', 'Germany', 'Active Business GmbH',
    'ACTIVE'::"SubscriptionStatus", true,
    NOW() - INTERVAL '15 days', NOW() + INTERVAL '15 days',
    NOW(), NOW()
);

-- PAST_DUE status
INSERT INTO "Company" (
    "id", "address", "city", "country", "name",
    "subscriptionStatus", "hasActiveSubscription",
    "subscriptionStartDate", "subscriptionEndDate",
    "createdAt", "updatedAt"
) VALUES (
    gen_random_uuid(),
    '987 Payment Street', 'Stuttgart', 'Germany', 'Payment Due Corp',
    'PAST_DUE'::"SubscriptionStatus", false,
    NOW() - INTERVAL '45 days', NOW() - INTERVAL '15 days',
    NOW(), NOW()
);

-- CANCELED status
INSERT INTO "Company" (
    "id", "address", "city", "country", "name",
    "subscriptionStatus", "hasActiveSubscription",
    "subscriptionStartDate", "subscriptionEndDate",
    "createdAt", "updatedAt"
) VALUES (
    gen_random_uuid(),
    '147 Canceled Ave', 'Dusseldorf', 'Germany', 'Former Customer GmbH',
    'CANCELED'::"SubscriptionStatus", false,
    NOW() - INTERVAL '60 days', NOW() - INTERVAL '30 days',
    NOW(), NOW()
);

-- EXPIRED status
INSERT INTO "Company" (
    "id", "address", "city", "country", "name",
    "subscriptionStatus", "hasActiveSubscription",
    "subscriptionStartDate", "subscriptionEndDate", "subscriptionExpiresAt",
    "createdAt", "updatedAt"
) VALUES (
    gen_random_uuid(),
    '258 Expired Boulevard', 'Leipzig', 'Germany', 'Expired Subscription Ltd',
    'EXPIRED'::"SubscriptionStatus", false,
    NOW() - INTERVAL '365 days', NOW() - INTERVAL '1 day', NOW() - INTERVAL '1 day',
    NOW(), NOW()
);

-- Query to verify the insertions
SELECT 
    "id",
    "name",
    "city",
    "subscriptionStatus",
    "hasActiveSubscription",
    "subscriptionStartDate",
    "subscriptionEndDate",
    "createdAt"
FROM "Company"
ORDER BY "createdAt" DESC
LIMIT 10;
