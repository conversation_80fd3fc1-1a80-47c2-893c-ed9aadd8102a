import { PrismaClient, BillingPeriod } from '@prisma/client';

const prisma = new PrismaClient();

async function seedPricingPlans() {
  console.log('🌱 Seeding pricing plans...');

  const pricingPlans = [
    {
      name: 'starter',
      displayName: 'Starter Plan',
      description: 'Perfect for small companies just getting started',
      features: [
        'Unlimited job advertisements',
        'Basic applicant tracking',
        'Email support',
        'Standard job visibility',
        'Basic analytics',
      ],
      priceInCents: 29900, // €299.00
      currency: 'EUR',
      billingPeriod: BillingPeriod.MONTHLY,
      durationDays: 30,
      isPopular: false,
      isCustom: false,
      sortOrder: 1,
      isActive: true,
      maxJobAdverts: null, // unlimited
      supportLevel: 'email',
    },
    {
      name: 'professional',
      displayName: 'Professional Plan',
      description: 'Ideal for growing companies with regular hiring needs',
      features: [
        'Everything in Starter',
        'Priority job placement',
        'Advanced applicant filtering',
        'Priority email support',
        'Advanced analytics & reports',
        'Bulk job management',
        'Custom branding options',
      ],
      priceInCents: 59900, // €599.00
      currency: 'EUR',
      billingPeriod: BillingPeriod.QUARTERLY,
      durationDays: 90,
      isPopular: true,
      isCustom: false,
      sortOrder: 2,
      isActive: true,
      maxJobAdverts: null, // unlimited
      supportLevel: 'priority',
    },
    {
      name: 'enterprise',
      displayName: 'Enterprise Plan',
      description: 'Complete solution for large organizations',
      features: [
        'Everything in Professional',
        'Dedicated account manager',
        'Custom integrations',
        'Phone & chat support',
        'API access',
        'Custom reporting',
        'White-label options',
        'SLA guarantee',
        'Training & onboarding',
      ],
      priceInCents: 199900, // €1999.00
      currency: 'EUR',
      billingPeriod: BillingPeriod.ANNUAL,
      durationDays: 365,
      isPopular: false,
      isCustom: false,
      sortOrder: 3,
      isActive: true,
      maxJobAdverts: null, // unlimited
      supportLevel: 'dedicated',
    },
  ];

  for (const plan of pricingPlans) {
    const existingPlan = await prisma.pricingPlan.findFirst({
      where: { name: plan.name },
    });

    if (!existingPlan) {
      await prisma.pricingPlan.create({
        data: {
          ...plan,
          features: plan.features, // Prisma will automatically convert to JSON
        },
      });
      console.log(`✅ Created ${plan.displayName}`);
    } else {
      console.log(`⏭️  ${plan.displayName} already exists`);
    }
  }

  console.log('✨ Pricing plans seeding completed!');
}

// Run the seed function
seedPricingPlans()
  .catch((e) => {
    console.error('❌ Error seeding pricing plans:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
