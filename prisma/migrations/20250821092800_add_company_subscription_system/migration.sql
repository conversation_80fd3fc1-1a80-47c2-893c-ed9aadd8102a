-- CreateEnum
CREATE TYPE "SubscriptionType" AS ENUM ('PER_JOB_ADVERT', 'COMPANY_UNLIMITED');

-- Create<PERSON>num
CREATE TYPE "BillingPeriod" AS ENUM ('MONTHLY', 'QUARTERLY', 'SE<PERSON>_ANNUAL', 'ANNUAL', 'CUSTOM');

-- AlterTable
ALTER TABLE "Company" ADD COLUMN     "hasActiveSubscription" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "subscriptionExpiresAt" TIMESTAMP(3);

-- AlterTable
ALTER TABLE "StripeSubscription" ADD COLUMN     "company_config_id" UUID,
ADD COLUMN     "pricing_plan_id" UUID,
ADD COLUMN     "subscriptionType" "SubscriptionType" NOT NULL DEFAULT 'COMPANY_UNLIMITED',
ALTER COLUMN "job_advert_id" DROP NOT NULL;

-- CreateTable
CREATE TABLE "PricingPlan" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "name" TEXT NOT NULL,
    "displayName" TEXT NOT NULL,
    "description" TEXT,
    "features" JSONB NOT NULL,
    "priceInCents" INTEGER NOT NULL,
    "currency" TEXT NOT NULL DEFAULT 'EUR',
    "billingPeriod" "BillingPeriod" NOT NULL,
    "durationDays" INTEGER NOT NULL,
    "isPopular" BOOLEAN NOT NULL DEFAULT false,
    "isCustom" BOOLEAN NOT NULL DEFAULT false,
    "sortOrder" INTEGER NOT NULL DEFAULT 0,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "maxJobAdverts" INTEGER,
    "supportLevel" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PricingPlan_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CompanySubscriptionConfig" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "company_id" UUID NOT NULL,
    "pricing_plan_id" UUID,
    "customPriceInCents" INTEGER,
    "customDurationDays" INTEGER,
    "stripeProductId" TEXT,
    "stripePriceId" TEXT,
    "notes" TEXT,
    "created_by" UUID,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CompanySubscriptionConfig_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SubscriptionAuditLog" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "company_id" UUID NOT NULL,
    "action" TEXT NOT NULL,
    "previousData" JSONB,
    "newData" JSONB NOT NULL,
    "performed_by" UUID,
    "reason" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "SubscriptionAuditLog_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "CompanySubscriptionConfig_company_id_key" ON "CompanySubscriptionConfig"("company_id");

-- AddForeignKey
ALTER TABLE "StripeSubscription" ADD CONSTRAINT "StripeSubscription_pricing_plan_id_fkey" FOREIGN KEY ("pricing_plan_id") REFERENCES "PricingPlan"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "StripeSubscription" ADD CONSTRAINT "StripeSubscription_company_config_id_fkey" FOREIGN KEY ("company_config_id") REFERENCES "CompanySubscriptionConfig"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CompanySubscriptionConfig" ADD CONSTRAINT "CompanySubscriptionConfig_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "Company"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CompanySubscriptionConfig" ADD CONSTRAINT "CompanySubscriptionConfig_pricing_plan_id_fkey" FOREIGN KEY ("pricing_plan_id") REFERENCES "PricingPlan"("id") ON DELETE SET NULL ON UPDATE CASCADE;
