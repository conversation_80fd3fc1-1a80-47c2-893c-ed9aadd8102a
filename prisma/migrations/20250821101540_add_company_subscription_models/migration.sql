-- AlterTable
ALTER TABLE "Company" ADD COLUMN     "contactEmail" TEXT,
ADD COLUMN     "current_plan_id" UUID,
ADD COLUMN     "subscriptionEndDate" TIMESTAMP(3),
ADD COLUMN     "subscriptionStartDate" TIMESTAMP(3),
ADD COLUMN     "subscriptionStatus" "SubscriptionStatus" DEFAULT 'PENDING';

-- AlterTable
ALTER TABLE "PricingPlan" ADD COLUMN     "displayOrder" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "hasAnalytics" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "hasApiAccess" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "hasCustomBranding" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "hasPrioritySupport" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "isFeatured" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "maxCandidates" INTEGER,
ADD COLUMN     "maxUsers" INTEGER,
ADD COLUMN     "planType" "PricingPlanType" NOT NULL DEFAULT 'STANDARD',
ADD COLUMN     "price" DOUBLE PRECISION NOT NULL DEFAULT 0,
ADD COLUMN     "stripePriceId" TEXT,
ADD COLUMN     "stripeProductId" TEXT,
ADD COLUMN     "subscriptionModel" "SubscriptionModel" NOT NULL DEFAULT 'COMPANY',
ADD COLUMN     "unlimitedCandidates" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "unlimitedJobAdverts" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "unlimitedUsers" BOOLEAN NOT NULL DEFAULT false;

-- AlterTable
ALTER TABLE "StripeSubscription" ADD COLUMN     "canceledAt" TIMESTAMP(3),
ADD COLUMN     "currentPeriodEnd" TIMESTAMP(3),
ADD COLUMN     "currentPeriodStart" TIMESTAMP(3),
ADD COLUMN     "lastPaymentAmount" DOUBLE PRECISION,
ADD COLUMN     "lastPaymentDate" TIMESTAMP(3),
ADD COLUMN     "metadata" JSONB,
ADD COLUMN     "stripePriceId" TEXT,
ADD COLUMN     "subscriptionModel" "SubscriptionModel" DEFAULT 'COMPANY';

-- CreateTable
CREATE TABLE "CompanySubscriptionUsage" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "company_id" UUID NOT NULL,
    "currentJobAdverts" INTEGER NOT NULL DEFAULT 0,
    "maxJobAdverts" INTEGER,
    "currentUsers" INTEGER NOT NULL DEFAULT 0,
    "maxUsers" INTEGER,
    "currentCandidates" INTEGER NOT NULL DEFAULT 0,
    "maxCandidates" INTEGER,
    "billingPeriodStart" TIMESTAMP(3),
    "billingPeriodEnd" TIMESTAMP(3),
    "lastUsageUpdate" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CompanySubscriptionUsage_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "StripeCustomer" (
    "id" TEXT NOT NULL,
    "email" TEXT,
    "name" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "StripeCustomer_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "CompanySubscriptionUsage_company_id_key" ON "CompanySubscriptionUsage"("company_id");

-- CreateIndex
CREATE UNIQUE INDEX "StripeCustomer_id_key" ON "StripeCustomer"("id");

-- AddForeignKey
ALTER TABLE "Company" ADD CONSTRAINT "Company_current_plan_id_fkey" FOREIGN KEY ("current_plan_id") REFERENCES "PricingPlan"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Company" ADD CONSTRAINT "Company_stripeCustomerId_fkey" FOREIGN KEY ("stripeCustomerId") REFERENCES "StripeCustomer"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CompanySubscriptionUsage" ADD CONSTRAINT "CompanySubscriptionUsage_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "Company"("id") ON DELETE CASCADE ON UPDATE CASCADE;
