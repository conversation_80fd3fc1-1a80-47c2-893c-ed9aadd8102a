// ============================================================================
// NEW SCHEMA ADDITIONS FOR COMPANY SUBSCRIPTION SYSTEM
// ============================================================================
// Add these to your main schema.prisma file

// ============================================================================
// ENUMS
// ============================================================================

enum SubscriptionType {
  PER_JOB_ADVERT    // Legacy per-job subscription
  COMPANY_UNLIMITED // New company-wide unlimited subscription
}

enum BillingPeriod {
  MONTHLY
  QUARTERLY
  SEMI_ANNUAL
  ANNUAL
  CUSTOM
}

// ============================================================================
// NEW MODELS
// ============================================================================

// Pricing plans that companies can choose from
model PricingPlan {
  id                String               @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name              String               // Internal name: "starter", "professional", "enterprise"
  displayName       String               // Display name: "Starter Plan", "Professional Plan"
  description       String?              // Marketing description
  features          Json                 // Array of feature strings
  priceInCents      Int                  // Price in cents to avoid decimal issues
  currency          String               @default("EUR")
  billingPeriod     BillingPeriod        
  durationDays      Int                  // 30, 90, 365, etc.
  isPopular         Boolean              @default(false) // Show "Most Popular" badge
  isCustom          Boolean              @default(false) // Custom pricing plan
  sortOrder         Int                  @default(0)     // Display order
  isActive          Boolean              @default(true)  // Is this plan available
  maxJobAdverts     Int?                 // null = unlimited
  supportLevel      String?              // "email", "priority", "dedicated"
  createdAt         DateTime             @default(now())
  updatedAt         DateTime             @updatedAt
  
  // Relations
  subscriptions     StripeSubscription[]
  companyConfigs    CompanySubscriptionConfig[]
}

// Company-specific subscription configuration
model CompanySubscriptionConfig {
  id                  String              @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  companyId           String              @unique @map("company_id") @db.Uuid
  company             Company             @relation(fields: [companyId], references: [id], onDelete: Cascade)
  pricingPlanId       String?             @map("pricing_plan_id") @db.Uuid // null = custom pricing
  pricingPlan         PricingPlan?        @relation(fields: [pricingPlanId], references: [id])
  customPriceInCents  Int?                // Override price for this company
  customDurationDays  Int?                // Override duration for this company
  stripeProductId     String?             // Dynamically created Stripe product ID
  stripePriceId       String?             // Dynamically created Stripe price ID
  notes               String?             // Admin notes about this configuration
  createdBy           String?             @map("created_by") @db.Uuid // SuperUser ID who created this
  createdAt           DateTime            @default(now())
  updatedAt           DateTime            @updatedAt
  
  // Relations
  subscriptions       StripeSubscription[]
}

// Audit log for subscription changes
model SubscriptionAuditLog {
  id            String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  companyId     String   @map("company_id") @db.Uuid
  action        String   // "CREATED", "UPDATED", "CANCELLED", "EXPIRED", "RENEWED"
  previousData  Json?    // Previous state
  newData       Json     // New state
  performedBy   String?  @map("performed_by") @db.Uuid // User ID who performed action
  reason        String?  // Reason for change
  createdAt     DateTime @default(now())
}

// ============================================================================
// UPDATES TO EXISTING MODELS
// ============================================================================

// Update Company model - add these fields:
model Company {
  // ... existing fields ...
  
  // New subscription fields
  hasActiveSubscription    Boolean                    @default(false)
  subscriptionExpiresAt    DateTime?
  subscriptionConfig       CompanySubscriptionConfig?
  
  // ... rest of existing fields ...
}

// Update StripeSubscription model - modify these fields:
model StripeSubscription {
  // ... existing fields ...
  
  // Updated fields
  subscriptionType    SubscriptionType              @default(COMPANY_UNLIMITED)
  pricingPlanId       String?                       @map("pricing_plan_id") @db.Uuid
  pricingPlan         PricingPlan?                  @relation(fields: [pricingPlanId], references: [id])
  companyConfigId     String?                       @map("company_config_id") @db.Uuid
  companyConfig       CompanySubscriptionConfig?    @relation(fields: [companyConfigId], references: [id])
  
  // Make jobAdvertId optional (for company-wide subscriptions)
  jobAdvertId         String?                       @map("job_advert_id") @db.Uuid // Now optional!
  jobAdvert           JobAdvert?                    @relation(fields: [jobAdvertId], references: [id], onDelete: Cascade) // Now optional!
  
  // ... rest of existing fields ...
}
