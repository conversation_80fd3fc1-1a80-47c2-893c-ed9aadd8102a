import {
    <PERSON>,
    <PERSON>,
    Req,
    <PERSON><PERSON>,
    BadRequestException,
    RawBodyRequest,
    Logger,
} from '@nestjs/common'
import { Request } from 'express'
import { StripeService } from '../../stripe/stripe.service'
import { ConfigService } from '@nestjs/config'
import Stripe from 'stripe'
import { CompanySubscriptionWebhookService } from '../company-subscription-webhook.service'

@Controller('stripe')
export class StripeWebhookController {
    private readonly logger = new Logger(StripeWebhookController.name)
    private readonly stripe: Stripe

    constructor(
        private readonly stripeService: StripeService,
        private readonly companySubscriptionWebhookService: CompanySubscriptionWebhookService,
        private readonly configService: ConfigService
    ) {
        this.stripe = this.stripeService.getStripe()
    }

    @Post('webhook')
    async handleWebhook(
        @Req() request: RawBodyRequest<Request>,
        @Headers('stripe-signature') signature: string
    ) {
        if (!signature) {
            throw new BadRequestException('Missing stripe-signature header')
        }

        const webhookSecret = this.configService.get<string>(
            'STRIPE_WEBHOOK_SECRET'
        )

        if (!webhookSecret) {
            this.logger.error('Stripe webhook secret not configured')
            throw new BadRequestException('Webhook not configured')
        }

        let event: Stripe.Event

        try {
            // Verify webhook signature
            event = this.stripe.webhooks.constructEvent(
                request.rawBody,
                signature,
                webhookSecret
            )
        } catch (err: any) {
            this.logger.error(
                `Webhook signature verification failed: ${err.message}`
            )
            throw new BadRequestException(`Webhook Error: ${err.message}`)
        }

        this.logger.log(`Processing webhook event: ${event.type}`)

        try {
            // Handle different event types
            switch (event.type) {
                case 'checkout.session.completed':
                    await this.companySubscriptionWebhookService.handleCheckoutSessionCompleted(
                        event.data.object as Stripe.Checkout.Session
                    )
                    break

                case 'customer.subscription.created':
                    await this.companySubscriptionWebhookService.handleSubscriptionCreated(
                        event
                    )
                    break

                case 'customer.subscription.updated':
                    await this.companySubscriptionWebhookService.handleSubscriptionUpdated(
                        event
                    )
                    break

                case 'customer.subscription.deleted':
                    await this.companySubscriptionWebhookService.handleSubscriptionDeleted(
                        event
                    )
                    break

                case 'invoice.payment_succeeded':
                    await this.companySubscriptionWebhookService.handleInvoicePaymentSucceeded(
                        event
                    )
                    break

                case 'invoice.payment_failed':
                    await this.companySubscriptionWebhookService.handleInvoicePaymentFailed(
                        event
                    )
                    break

                case 'customer.subscription.trial_will_end':
                    this.logger.log(
                        `Trial will end for subscription: ${
                            (event.data.object as Stripe.Subscription).id
                        }`
                    )
                    break

                default:
                    this.logger.log(`Unhandled event type: ${event.type}`)
            }

            return { received: true }
        } catch (error: any) {
            this.logger.error(
                `Error processing webhook: ${error.message}`,
                error.stack
            )
            // Return success to avoid Stripe retrying
            return { received: true, error: error.message }
        }
    }
}
