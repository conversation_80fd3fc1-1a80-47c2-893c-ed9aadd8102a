import { ObjectType, Field, Int, ID, registerEnumType } from '@nestjs/graphql';
import { BillingPeriod } from '@prisma/client';
import GraphQLJSON from 'graphql-type-json';

// Register the enum with GraphQL
registerEnumType(BillingPeriod, {
    name: 'BillingPeriod',
    description: 'The billing period for a subscription',
});

@ObjectType()
export class PricingPlan {
    @Field(() => ID)
    id: string;

    @Field()
    name: string;

    @Field()
    displayName: string;

    @Field({ nullable: true })
    description?: string;

    @Field(() => GraphQLJSON)
    features: any;

    @Field(() => Int)
    priceInCents: number;

    @Field()
    currency: string;

    @Field(() => BillingPeriod)
    billingPeriod: BillingPeriod;

    @Field(() => Int)
    durationDays: number;

    @Field()
    isPopular: boolean;

    @Field()
    isCustom: boolean;

    @Field(() => Int)
    sortOrder: number;

    @Field()
    isActive: boolean;

    @Field(() => Int, { nullable: true })
    maxJobAdverts?: number;

    @Field(() => Int, { nullable: true })
    maxUsers?: number;

    @Field(() => Int, { nullable: true })
    maxCandidates?: number;

    @Field({ nullable: true })
    supportLevel?: string;

    @Field({ nullable: true })
    stripePriceId?: string;

    @Field(() => Int, { nullable: true })
    price?: number;

    @Field()
    createdAt: Date;

    @Field()
    updatedAt: Date;
}
