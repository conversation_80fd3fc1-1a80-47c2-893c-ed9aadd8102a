import { ObjectType, Field, Int, registerEnumType } from '@nestjs/graphql';
import { BillingPeriod, PricingPlanType, SubscriptionModel } from '@prisma/client';
import GraphQLJSON from 'graphql-type-json';

// Register the enums with GraphQL
registerEnumType(BillingPeriod, {
    name: 'BillingPeriod',
    description: 'The billing period for a subscription',
});

registerEnumType(PricingPlanType, {
    name: 'PricingPlanType',
    description: 'The type of pricing plan',
});

registerEnumType(SubscriptionModel, {
    name: 'SubscriptionModel',
    description: 'The subscription model',
});

@ObjectType()
export class PricingPlan {
    @Field()
    id: string;

    @Field()
    name: string;

    @Field()
    displayName: string;

    @Field({ nullable: true })
    description?: string;

    @Field(() => GraphQLJSON)
    features: any;

    @Field(() => Int)
    priceInCents: number;

    @Field()
    currency: string;

    @Field(() => BillingPeriod)
    billingPeriod: BillingPeriod;

    @Field(() => Int)
    durationDays: number;

    @Field()
    isPopular: boolean;

    @Field()
    isCustom: boolean;

    @Field(() => Int)
    sortOrder: number;

    @Field()
    isActive: boolean;

    @Field(() => Int, { nullable: true })
    maxJobAdverts?: number;

    @Field(() => Int, { nullable: true })
    maxUsers?: number;

    @Field(() => Int, { nullable: true })
    maxCandidates?: number;

    @Field({ nullable: true })
    supportLevel?: string;

    @Field({ nullable: true })
    stripePriceId?: string;

    @Field({ nullable: true })
    stripeProductId?: string;

    @Field(() => Int, { nullable: true })
    price?: number;

    @Field()
    unlimitedJobAdverts: boolean;

    @Field()
    unlimitedUsers: boolean;

    @Field()
    unlimitedCandidates: boolean;

    @Field()
    hasAnalytics: boolean;

    @Field()
    hasPrioritySupport: boolean;

    @Field()
    hasCustomBranding: boolean;

    @Field()
    hasApiAccess: boolean;

    @Field(() => PricingPlanType)
    planType: PricingPlanType;

    @Field(() => SubscriptionModel)
    subscriptionModel: SubscriptionModel;

    @Field()
    displayOrder: number;

    @Field()
    isFeatured: boolean;

    @Field()
    createdAt: Date;

    @Field()
    updatedAt: Date;
}
