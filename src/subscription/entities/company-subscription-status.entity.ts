import { ObjectType, Field, Int } from '@nestjs/graphql';
import { PricingPlan } from './pricing-plan.entity';

@ObjectType()
export class SubscriptionUsage {
    @Field(() => Int)
    currentJobAdverts: number;

    @Field(() => Int)
    maxJobAdverts: number;

    @Field(() => Int, { nullable: true })
    currentUsers?: number;

    @Field(() => Int, { nullable: true })
    maxUsers?: number;

    @Field(() => Int, { nullable: true })
    currentCandidates?: number;

    @Field(() => Int, { nullable: true })
    maxCandidates?: number;
}

@ObjectType()
export class CompanySubscriptionStatus {
    @Field()
    hasActiveSubscription: boolean;

    @Field({ nullable: true })
    expiresAt?: Date;

    @Field(() => PricingPlan, { nullable: true })
    currentPlan?: PricingPlan;

    @Field(() => SubscriptionUsage, { nullable: true })
    usage?: SubscriptionUsage;

    @Field(() => Date, { nullable: true })
    subscriptionEndDate?: Date;

    @Field(() => Int)
    jobAdvertsUsed: number;

    @Field(() => Int, { nullable: true })
    jobAdvertsLimit?: number;

    @Field(() => Int, { nullable: true })
    daysRemaining?: number;

    @Field()
    autoRenew: boolean;

    @Field({ nullable: true })
    subscriptionType?: string;
}
