import { MiddlewareConsumer, Module } from '@nestjs/common'
import { ConfigModule, ConfigService } from '@nestjs/config'
import { StripeModule } from '@golevelup/nestjs-stripe'
import { HttpModule } from '@nestjs/axios'
import { RawBodyMiddleware } from '@golevelup/nestjs-webhooks'
import { SubscriptionService } from './subscription.service'
import { SubscriptionResolver } from './subscription.resolver'
import { SubscriptionController } from './subscription.controller'
import { StripeWebhookController } from './webhooks/stripe-webhook.controller'
import { StripeDynamicProductService } from '../stripe/stripe-dynamic-product.service'
import { PricingPlanService } from './pricing-plan.service'
import { PricingPlanResolver } from './pricing-plan.resolver'
import { CompanySubscriptionService } from './company-subscription.service'
import { CompanySubscriptionResolver } from './company-subscription.resolver'
import { CompanySubscriptionWebhookService } from './company-subscription-webhook.service'
import { StripeService } from '../stripe/stripe.service'
import { JobActionsModule } from '../job-actions/job-actions.module'
import { AuthModule } from '../auth/auth.module'
import { CompanyModule } from '../company/company.module'
import { JobAdvertModule } from '../job-advert/job-advert.module'
import { PrismaService } from '../prisma.service'

@Module({
    imports: [
        StripeModule.forRootAsync(StripeModule, {
            imports: [ConfigModule],
            useFactory: (configService: ConfigService) =>
                configService.get('STRIPE_CONFIG'),
            inject: [ConfigService],
        }),
        HttpModule,
        JobActionsModule,
        JobAdvertModule,
        AuthModule,
        CompanyModule,
    ],
    providers: [
        SubscriptionResolver,
        SubscriptionService,
        StripeService,
        StripeDynamicProductService,
        PricingPlanService,
        PricingPlanResolver,
        CompanySubscriptionService,
        CompanySubscriptionResolver,
        CompanySubscriptionWebhookService,
        PrismaService,
    ],
    controllers: [SubscriptionController, StripeWebhookController],
    exports: [
        SubscriptionService,
        StripeService,
        StripeDynamicProductService,
        PricingPlanService,
        CompanySubscriptionService,
    ],
})
export class SubscriptionModule {
    configure(consumer: MiddlewareConsumer) {
        consumer
            .apply(RawBodyMiddleware)
            .forRoutes('subscription/handle-webhook', 'stripe/webhook')
    }
}
