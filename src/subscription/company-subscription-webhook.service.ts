import { Injectable, Logger } from '@nestjs/common'
import { StripeWebhookHandler } from '@golevelup/nestjs-stripe'
import Stripe from 'stripe'
import { CompanySubscriptionService } from './company-subscription.service'
import { PrismaService } from '../prisma.service'
import { SubscriptionStatus, SubscriptionModel } from '@prisma/client'

@Injectable()
export class CompanySubscriptionWebhookService {
    private readonly logger = new Logger(CompanySubscriptionWebhookService.name)

    constructor(
        private readonly companySubscriptionService: CompanySubscriptionService,
        private readonly prismaService: PrismaService
    ) {}

    /**
     * Handle successful checkout session completion
     */
    @StripeWebhookHandler('checkout.session.completed')
    async handleCheckoutSessionCompleted(
        event: Stripe.Checkout.Session
    ): Promise<void> {
        this.logger.log('Handling checkout.session.completed event')

        const session = event.data.object as Stripe.Checkout.Session

        // Check if this is a company subscription
        if (session.metadata?.subscriptionModel === SubscriptionModel.COMPANY) {
            await this.companySubscriptionService.handleCheckoutSessionCompleted(
                session
            )
        }
    }

    /**
     * Handle payment succeeded
     */
    @StripeWebhookHandler('payment_intent.succeeded')
    async handlePaymentIntentSucceeded(event: Stripe.Event): Promise<void> {
        this.logger.log('Handling payment_intent.succeeded event')

        const paymentIntent = event.data.object as Stripe.PaymentIntent

        // Update subscription payment status if needed
        if (paymentIntent.metadata?.companyId) {
            await this.prismaService.stripeSubscription.updateMany({
                where: {
                    companyId: paymentIntent.metadata.companyId,
                    stripeCustomerId: paymentIntent.customer as string,
                },
                data: {
                    lastPaymentDate: new Date(),
                    lastPaymentAmount: paymentIntent.amount / 100,
                    metadata: {
                        lastPaymentIntentId: paymentIntent.id,
                    },
                },
            })
        }
    }

    /**
     * Handle subscription created
     */
    @StripeWebhookHandler('customer.subscription.created')
    async handleSubscriptionCreated(event: Stripe.Subscription): Promise<void> {
        this.logger.log('Handling customer.subscription.created event')

        const subscription = event.data.object as Stripe.Subscription

        // Create or update subscription record
        await this.handleSubscriptionUpdate(subscription, 'created')
    }

    /**
     * Handle subscription updated
     */
    @StripeWebhookHandler('customer.subscription.updated')
    async handleSubscriptionUpdated(event: Stripe.Event): Promise<void> {
        this.logger.log('Handling customer.subscription.updated event')

        const subscription = event.data.object as Stripe.Subscription

        // Update subscription record
        await this.handleSubscriptionUpdate(subscription, 'updated')
    }

    /**
     * Handle subscription deleted/cancelled
     */
    @StripeWebhookHandler('customer.subscription.deleted')
    async handleSubscriptionDeleted(event: Stripe.Event): Promise<void> {
        this.logger.log('Handling customer.subscription.deleted event')

        const subscription = event.data.object as Stripe.Subscription

        // Find the company associated with this subscription
        const stripeSubscription =
            await this.prismaService.stripeSubscription.findFirst({
                where: {
                    stripeSubscriptionId: subscription.id,
                },
                include: {
                    company: true,
                },
            })

        if (stripeSubscription && stripeSubscription.company) {
            // Update subscription status
            await this.prismaService.stripeSubscription.update({
                where: { id: stripeSubscription.id },
                data: {
                    status: 'canceled',
                    canceledAt: new Date(),
                },
            })

            // Update company subscription status
            await this.prismaService.company.update({
                where: { id: stripeSubscription.companyId },
                data: {
                    subscriptionStatus: SubscriptionStatus.CANCELED,
                },
            })

            this.logger.log(
                `Subscription cancelled for company ${stripeSubscription.company.name}`
            )
        }
    }

    /**
     * Handle invoice payment succeeded
     */
    @StripeWebhookHandler('invoice.payment_succeeded')
    async handleInvoicePaymentSucceeded(event: Stripe.Event): Promise<void> {
        this.logger.log('Handling invoice.payment_succeeded event')

        const invoice = event.data.object as Stripe.Invoice

        if (invoice.subscription) {
            const subscription =
                await this.prismaService.stripeSubscription.findFirst({
                    where: {
                        stripeSubscriptionId: invoice.subscription as string,
                    },
                })

            if (subscription) {
                // Update payment information
                await this.prismaService.stripeSubscription.update({
                    where: { id: subscription.id },
                    data: {
                        lastPaymentDate: new Date(),
                        lastPaymentAmount: invoice.amount_paid / 100,
                        metadata: {
                            lastInvoiceId: invoice.id,
                            lastInvoiceNumber: invoice.number,
                        },
                    },
                })

                // Reset billing period usage if needed
                if (subscription.companyId) {
                    await this.companySubscriptionService.resetBillingPeriodUsage(
                        subscription.companyId
                    )
                }
            }
        }
    }

    /**
     * Handle invoice payment failed
     */
    @StripeWebhookHandler('invoice.payment_failed')
    async handleInvoicePaymentFailed(event: Stripe.Event): Promise<void> {
        this.logger.log('Handling invoice.payment_failed event')

        const invoice = event.data.object as Stripe.Invoice

        if (invoice.subscription) {
            const subscription =
                await this.prismaService.stripeSubscription.findFirst({
                    where: {
                        stripeSubscriptionId: invoice.subscription as string,
                    },
                    include: {
                        company: true,
                    },
                })

            if (subscription && subscription.company) {
                // Update subscription to past_due status
                await this.prismaService.stripeSubscription.update({
                    where: { id: subscription.id },
                    data: {
                        status: 'past_due',
                    },
                })

                // Update company subscription status
                await this.prismaService.company.update({
                    where: { id: subscription.companyId },
                    data: {
                        subscriptionStatus: SubscriptionStatus.PAST_DUE,
                    },
                })

                this.logger.warn(
                    `Payment failed for company ${subscription.company.name}`
                )

                // TODO: Send payment failed email notification
            }
        }
    }

    /**
     * Handle subscription trial ending
     */
    @StripeWebhookHandler('customer.subscription.trial_will_end')
    async handleSubscriptionTrialWillEnd(event: Stripe.Event): Promise<void> {
        this.logger.log('Handling customer.subscription.trial_will_end event')

        const subscription = event.data.object as Stripe.Subscription

        const stripeSubscription =
            await this.prismaService.stripeSubscription.findFirst({
                where: {
                    stripeSubscriptionId: subscription.id,
                },
                include: {
                    company: true,
                },
            })

        if (stripeSubscription && stripeSubscription.company) {
            this.logger.log(
                `Trial ending soon for company ${stripeSubscription.company.name}`
            )

            // TODO: Send trial ending email notification
        }
    }

    /**
     * Helper method to handle subscription updates
     */
    private async handleSubscriptionUpdate(
        subscription: Stripe.Subscription,
        action: 'created' | 'updated'
    ): Promise<void> {
        // Find the company associated with this subscription
        const company = await this.prismaService.company.findFirst({
            where: {
                stripeCustomerId: subscription.customer as string,
            },
        })

        if (company) {
            // Map Stripe status to our status
            let status: SubscriptionStatus
            switch (subscription.status) {
                case 'active':
                    status = SubscriptionStatus.ACTIVE
                    break
                case 'trialing':
                    status = SubscriptionStatus.TRIAL
                    break
                case 'past_due':
                    status = SubscriptionStatus.PAST_DUE
                    break
                case 'canceled':
                case 'incomplete_expired':
                    status = SubscriptionStatus.CANCELED
                    break
                default:
                    status = SubscriptionStatus.PENDING
            }

            // Update or create subscription record
            const existingSubscription =
                await this.prismaService.stripeSubscription.findFirst({
                    where: {
                        stripeSubscriptionId: subscription.id,
                    },
                })

            if (existingSubscription) {
                // Update existing subscription
                await this.prismaService.stripeSubscription.update({
                    where: { id: existingSubscription.id },
                    data: {
                        status: subscription.status,
                        currentPeriodStart: new Date(
                            subscription.current_period_start * 1000
                        ),
                        currentPeriodEnd: new Date(
                            subscription.current_period_end * 1000
                        ),
                        cancelAtPeriodEnd: subscription.cancel_at_period_end,
                        canceledAt: subscription.canceled_at
                            ? new Date(subscription.canceled_at * 1000)
                            : null,
                    },
                })
            } else {
                // Create new subscription record
                const priceId = subscription.items.data[0]?.price.id

                // Find the pricing plan associated with this price
                const pricingPlan =
                    await this.prismaService.pricingPlan.findFirst({
                        where: {
                            stripePriceId: priceId,
                        },
                    })

                await this.prismaService.stripeSubscription.create({
                    data: {
                        stripeSubscriptionId: subscription.id,
                        stripeCustomerId: subscription.customer as string,
                        stripePriceId: priceId,
                        status: subscription.status,
                        currentPeriodStart: new Date(
                            subscription.current_period_start * 1000
                        ),
                        currentPeriodEnd: new Date(
                            subscription.current_period_end * 1000
                        ),
                        cancelAtPeriodEnd: subscription.cancel_at_period_end,
                        subscriptionModel: SubscriptionModel.COMPANY,
                        companyId: company.id,
                        pricingPlanId: pricingPlan?.id,
                    },
                })
            }

            // Update company subscription status
            await this.prismaService.company.update({
                where: { id: company.id },
                data: {
                    subscriptionStatus: status,
                    subscriptionEndDate: new Date(
                        subscription.current_period_end * 1000
                    ),
                },
            })

            this.logger.log(
                `Subscription ${action} for company ${company.name}`
            )
        }
    }
}
