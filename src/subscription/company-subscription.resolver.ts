import { Resolver, Query, Mutation, Args } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { CompanySubscriptionService } from './company-subscription.service';
import { CompanySubscriptionStatus } from './entities/company-subscription-status.entity';
import { CreateCompanyCheckoutSessionInput } from './dto/create-company-checkout-session.input';
import { GraphqlAuthGuard } from '../auth/graphql-auth/graphql-auth.guard';

@Resolver()
export class CompanySubscriptionResolver {
    constructor(
        private readonly companySubscriptionService: CompanySubscriptionService
    ) {}

    /**
     * Create a checkout session for company subscription
     */
    @Mutation(() => String, { name: 'createCompanyCheckoutSession' })
    @UseGuards(GraphqlAuthGuard)
    async createCompanyCheckoutSession(
        @Args('input') input: CreateCompanyCheckoutSessionInput
    ): Promise<string> {
        // TODO: Verify user has permission to create subscription for this company
        return this.companySubscriptionService.createCheckoutSession(input);
    }

    /**
     * Get current company subscription status
     */
    @Query(() => CompanySubscriptionStatus, { name: 'companySubscriptionStatus' })
    @UseGuards(GraphqlAuthGuard)
    async getCompanySubscriptionStatus(
        @Args('companyId') companyId: string
    ): Promise<CompanySubscriptionStatus> {
        // TODO: Verify user has permission to view this company's subscription
        const status = await this.companySubscriptionService.getCompanySubscriptionStatus(companyId);
        
        return {
            hasActiveSubscription: status.subscriptionStatus === 'ACTIVE',
            expiresAt: status.subscriptionEndDate,
            currentPlan: status.currentPlan,
            usage: status.usage ? {
                currentJobAdverts: status.usage.currentJobAdverts,
                maxJobAdverts: status.usage.maxJobAdverts,
                currentUsers: status.usage.currentUsers,
                maxUsers: status.usage.maxUsers,
                currentCandidates: status.usage.currentCandidates,
                maxCandidates: status.usage.maxCandidates
            } : null,
            subscriptionEndDate: status.subscriptionEndDate,
            jobAdvertsUsed: status.usage?.currentJobAdverts || 0,
            jobAdvertsLimit: status.usage?.maxJobAdverts || 0,
            daysRemaining: status.daysRemaining,
            autoRenew: !status.activeSubscription?.cancelAtPeriodEnd,
            subscriptionType: 'COMPANY'
        };
    }

    /**
     * Check if company can create a new job advert
     */
    @Query(() => Boolean, { name: 'canCreateJobAdvert' })
    @UseGuards(GraphqlAuthGuard)
    async canCreateJobAdvert(
        @Args('companyId') companyId: string
    ): Promise<boolean> {
        // TODO: Verify user has permission to check this company's limits
        return this.companySubscriptionService.canCreateJobAdvert(companyId);
    }

    /**
     * Cancel company subscription
     */
    @Mutation(() => Boolean, { name: 'cancelCompanySubscription' })
    @UseGuards(GraphqlAuthGuard)
    async cancelCompanySubscription(
        @Args('companyId') companyId: string
    ): Promise<boolean> {
        // TODO: Verify user has permission to cancel this company's subscription
        await this.companySubscriptionService.cancelSubscription(companyId);
        return true;
    }

    /**
     * Change company subscription plan
     */
    @Mutation(() => String, { name: 'changeCompanyPlan' })
    @UseGuards(GraphqlAuthGuard)
    async changeCompanyPlan(
        @Args('companyId') companyId: string,
        @Args('newPlanId') newPlanId: string
    ): Promise<string> {
        // TODO: Verify user has permission to change this company's plan
        return this.companySubscriptionService.changePlan(companyId, newPlanId);
    }

    /**
     * Increment job advert usage (called when creating a new job advert)
     */
    @Mutation(() => Boolean, { name: 'incrementJobAdvertUsage' })
    @UseGuards(GraphqlAuthGuard)
    async incrementJobAdvertUsage(
        @Args('companyId') companyId: string
    ): Promise<boolean> {
        // TODO: Verify user has permission to create job adverts for this company
        await this.companySubscriptionService.incrementJobAdvertUsage(companyId);
        return true;
    }

    /**
     * Get subscription metrics (Admin only)
     */
    @Query(() => String, { name: 'subscriptionMetrics' })
    @UseGuards(GraphqlAuthGuard) // TODO: Add SuperAdmin guard
    async getSubscriptionMetrics(): Promise<string> {
        const metrics = await this.companySubscriptionService.getSubscriptionMetrics();
        return JSON.stringify(metrics);
    }

    /**
     * Reset billing period usage (typically called by a cron job)
     */
    @Mutation(() => Boolean, { name: 'resetBillingPeriodUsage' })
    @UseGuards(GraphqlAuthGuard) // TODO: Add SuperAdmin guard
    async resetBillingPeriodUsage(
        @Args('companyId') companyId: string
    ): Promise<boolean> {
        await this.companySubscriptionService.resetBillingPeriodUsage(companyId);
        return true;
    }
}
