import { Resolver, Query, Mutation, Args } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { PricingPlanService } from './pricing-plan.service';
import { PricingPlan } from './entities/pricing-plan.entity';
import { CreatePricingPlanInput, UpdatePricingPlanInput } from './dto/create-pricing-plan.input';
import { GraphqlAuthGuard } from '../auth/graphql-auth/graphql-auth.guard';

@Resolver(() => PricingPlan)
export class PricingPlanResolver {
    constructor(private readonly pricingPlanService: PricingPlanService) {}

    /**
     * Get all active pricing plans for display to customers
     */
    @Query(() => [PricingPlan], { name: 'pricingPlans' })
    async getPricingPlans(): Promise<PricingPlan[]> {
        return this.pricingPlanService.getActivePlans();
    }

    /**
     * Get a specific pricing plan by ID
     */
    @Query(() => PricingPlan, { name: 'pricingPlan' })
    async getPricingPlan(
        @Args('id') id: string
    ): Promise<PricingPlan> {
        return this.pricingPlanService.getPlanById(id);
    }

    /**
     * Get recommended plan for a company
     */
    @Query(() => PricingPlan, { name: 'recommendedPlan', nullable: true })
    @UseGuards(GraphqlAuthGuard)
    async getRecommendedPlan(
        @Args('companySize', { type: () => Number, nullable: true }) companySize?: number
    ): Promise<PricingPlan | null> {
        return this.pricingPlanService.getRecommendedPlan(companySize);
    }

    /**
     * Admin: Get all pricing plans including inactive and custom ones
     */
    @Query(() => [PricingPlan], { name: 'allPricingPlansAdmin' })
    @UseGuards(GraphqlAuthGuard) // TODO: Add SuperAdmin guard
    async getAllPricingPlansAdmin(): Promise<PricingPlan[]> {
        return this.pricingPlanService.getAllPlansForAdmin();
    }

    /**
     * Admin: Create a new pricing plan
     */
    @Mutation(() => PricingPlan, { name: 'createPricingPlan' })
    @UseGuards(GraphqlAuthGuard) // TODO: Add SuperAdmin guard
    async createPricingPlan(
        @Args('input') input: CreatePricingPlanInput
    ): Promise<PricingPlan> {
        return this.pricingPlanService.createCustomPlan(input);
    }

    /**
     * Admin: Update a pricing plan
     */
    @Mutation(() => PricingPlan, { name: 'updatePricingPlan' })
    @UseGuards(GraphqlAuthGuard) // TODO: Add SuperAdmin guard
    async updatePricingPlan(
        @Args('id') id: string,
        @Args('input') input: UpdatePricingPlanInput
    ): Promise<PricingPlan> {
        return this.pricingPlanService.updatePlan(id, input);
    }

    /**
     * Admin: Deactivate a pricing plan
     */
    @Mutation(() => PricingPlan, { name: 'deactivatePricingPlan' })
    @UseGuards(GraphqlAuthGuard) // TODO: Add SuperAdmin guard
    async deactivatePricingPlan(
        @Args('id') id: string
    ): Promise<PricingPlan> {
        return this.pricingPlanService.deactivatePlan(id);
    }
}
