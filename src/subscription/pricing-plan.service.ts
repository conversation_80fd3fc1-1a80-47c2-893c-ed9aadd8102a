import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma.service';
import { StripeService } from '../stripe/stripe.service';
import { StripeDynamicProductService } from '../stripe/stripe-dynamic-product.service';
import { PricingPlan } from './entities/pricing-plan.entity';
import { CreatePricingPlanInput, UpdatePricingPlanInput, PricingPlanType } from './dto/create-pricing-plan.input';
import { 
    PricingPlan as PrismaPricingPlan, 
    BillingPeriod,
    SubscriptionModel
} from '@prisma/client';

@Injectable()
export class PricingPlanService {
    constructor(
        private readonly prismaService: PrismaService,
        private readonly stripeService: StripeService,
        private readonly stripeDynamicProductService: StripeDynamicProductService
    ) {}

    /**
     * Get all active pricing plans for customer display
     */
    async getActivePlans(): Promise<PricingPlan[]> {
        const plans = await this.prismaService.pricingPlan.findMany({
            where: {
                isActive: true,
                planType: {
                    in: [PricingPlanType.STANDARD, PricingPlanType.PROMOTIONAL]
                }
            },
            orderBy: {
                displayOrder: 'asc'
            }
        });

        return plans.map(this.mapPricingPlanToEntity);
    }

    /**
     * Get a specific pricing plan by ID
     */
    async getPlanById(id: string): Promise<PricingPlan> {
        const plan = await this.prismaService.pricingPlan.findUnique({
            where: { id }
        });

        if (!plan) {
            throw new NotFoundException(`Pricing plan with ID ${id} not found`);
        }

        return this.mapPricingPlanToEntity(plan);
    }

    /**
     * Get a pricing plan by name
     */
    async getPlanByName(name: string): Promise<PricingPlan | null> {
        const plan = await this.prismaService.pricingPlan.findFirst({
            where: { name },
        });
        return plan ? this.mapPricingPlanToEntity(plan) : null;
    }

    /**
     * Create a custom pricing plan
     */
    async createCustomPlan(input: CreatePricingPlanInput): Promise<PricingPlan> {
        // Validate input
        if (input.price < 0) {
            throw new BadRequestException('Price cannot be negative');
        }

        if (input.maxJobAdverts && input.maxJobAdverts < 0) {
            throw new BadRequestException('Max job adverts cannot be negative');
        }

        // Create Stripe product and price if needed
        let stripeProductId: string | null = null;
        let stripePriceId: string | null = null;

        if (input.createStripeProduct) {
            const stripeProduct = await this.stripeDynamicProductService.createProduct({
                name: input.name,
                description: input.description || undefined,
                metadata: {
                    planType: input.planType || PricingPlanType.CUSTOM,
                    billingPeriod: input.billingPeriod,
                    maxJobAdverts: input.maxJobAdverts?.toString() || 'unlimited'
                }
            });

            stripeProductId = stripeProduct.id;

            // Create price for the product
            const stripePrice = await this.stripeDynamicProductService.createPrice({
                productId: stripeProductId,
                unitAmount: Math.round(input.price * 100), // Convert to cents
                currency: input.currency || 'usd',
                billingPeriod: input.billingPeriod
            });

            stripePriceId = stripePrice.id;
        }

        // Create the pricing plan in database
        const plan = await this.prismaService.pricingPlan.create({
            data: {
                name: input.name,
                description: input.description,
                price: input.price,
                currency: input.currency || 'USD',
                billingPeriod: input.billingPeriod,
                features: input.features || [],
                maxJobAdverts: input.maxJobAdverts,
                maxUsers: input.maxUsers,
                maxCandidates: input.maxCandidates,
                unlimitedJobAdverts: input.unlimitedJobAdverts || false,
                unlimitedUsers: input.unlimitedUsers || false,
                unlimitedCandidates: input.unlimitedCandidates || false,
                hasAnalytics: input.hasAnalytics || false,
                hasPrioritySupport: input.hasPrioritySupport || false,
                hasCustomBranding: input.hasCustomBranding || false,
                hasApiAccess: input.hasApiAccess || false,
                planType: input.planType || PricingPlanType.CUSTOM,
                subscriptionModel: input.subscriptionModel || SubscriptionModel.COMPANY,
                displayOrder: input.displayOrder || 999,
                isActive: input.isActive ?? true,
                isFeatured: input.isFeatured || false,
                stripeProductId,
                stripePriceId
            }
        });

        return this.mapPricingPlanToEntity(plan);
    }

    /**
     * Update an existing pricing plan
     */
    async updatePlan(id: string, input: UpdatePricingPlanInput): Promise<PricingPlan> {
        // Check if plan exists
        const existingPlan = await this.prismaService.pricingPlan.findUnique({
            where: { id }
        });

        if (!existingPlan) {
            throw new NotFoundException(`Pricing plan with ID ${id} not found`);
        }

        // If price or billing period changed and Stripe product exists, create new price
        let newStripePriceId: string | null = null;
        
        if (existingPlan.stripeProductId && 
            (input.price !== undefined || input.billingPeriod !== undefined)) {
            
            const newPrice = input.price ?? existingPlan.price;
            const newBillingPeriod = input.billingPeriod ?? existingPlan.billingPeriod;
            
            if (newPrice !== existingPlan.price || newBillingPeriod !== existingPlan.billingPeriod) {
                // Archive old price if it exists
                if (existingPlan.stripePriceId) {
                    await this.stripeDynamicProductService.archivePrice(existingPlan.stripePriceId);
                }
                
                // Create new price
                const stripePrice = await this.stripeDynamicProductService.createPrice({
                    productId: existingPlan.stripeProductId,
                    unitAmount: Math.round(newPrice * 100),
                    currency: input.currency || existingPlan.currency,
                    billingPeriod: newBillingPeriod
                });
                
                newStripePriceId = stripePrice.id;
            }
        }

        // Update the plan
        const updatedPlan = await this.prismaService.pricingPlan.update({
            where: { id },
            data: {
                name: input.name,
                description: input.description,
                price: input.price,
                currency: input.currency,
                billingPeriod: input.billingPeriod,
                features: input.features,
                maxJobAdverts: input.maxJobAdverts,
                maxUsers: input.maxUsers,
                maxCandidates: input.maxCandidates,
                unlimitedJobAdverts: input.unlimitedJobAdverts,
                unlimitedUsers: input.unlimitedUsers,
                unlimitedCandidates: input.unlimitedCandidates,
                hasAnalytics: input.hasAnalytics,
                hasPrioritySupport: input.hasPrioritySupport,
                hasCustomBranding: input.hasCustomBranding,
                hasApiAccess: input.hasApiAccess,
                planType: input.planType,
                subscriptionModel: input.subscriptionModel,
                displayOrder: input.displayOrder,
                isActive: input.isActive,
                isFeatured: input.isFeatured,
                stripePriceId: newStripePriceId || undefined
            }
        });

        return this.mapPricingPlanToEntity(updatedPlan);
    }

    /**
     * Deactivate a pricing plan
     */
    async deactivatePlan(id: string): Promise<PricingPlan> {
        const plan = await this.prismaService.pricingPlan.findUnique({
            where: { id }
        });

        if (!plan) {
            throw new NotFoundException(`Pricing plan with ID ${id} not found`);
        }

        // Archive Stripe product if it exists
        if (plan.stripeProductId) {
            await this.stripeDynamicProductService.archiveProduct(plan.stripeProductId);
        }

        // Deactivate the plan
        const updatedPlan = await this.prismaService.pricingPlan.update({
            where: { id },
            data: {
                isActive: false
            }
        });

        return this.mapPricingPlanToEntity(updatedPlan);
    }

    /**
     * Get recommended plan based on company size
     */
    async getRecommendedPlan(companySize?: number): Promise<PricingPlan | null> {
        // Simple logic: recommend based on company size
        let recommendedPlanName = 'starter';
        
        if (companySize) {
            if (companySize >= 100) {
                recommendedPlanName = 'enterprise';
            } else if (companySize >= 20) {
                recommendedPlanName = 'professional';
            }
        }

        return this.getPlanByName(recommendedPlanName);
    }

    /**
     * Get all plans for admin view (including inactive and custom)
     */
    async getAllPlansForAdmin(): Promise<PricingPlan[]> {
        const plans = await this.prismaService.pricingPlan.findMany({
            orderBy: [
                { planType: 'asc' },
                { displayOrder: 'asc' },
                { createdAt: 'desc' }
            ]
        });

        return plans.map(this.mapPricingPlanToEntity);
    }

    /**
     * Validate if a plan can be used for subscription
     */
    async validatePlanForSubscription(planId: string): Promise<boolean> {
        const plan = await this.getPlanById(planId);
        return plan.isActive;
    }

    /**
     * Get plan features as array
     */
    getPlanFeatures(plan: PricingPlan): string[] {
        if (Array.isArray(plan.features)) {
            return plan.features as string[];
        }
        if (typeof plan.features === 'object' && plan.features !== null) {
            // If it's stored as an object, try to extract an array
            return (plan.features as any).features || [];
        }
        return [];
    }

    /**
     * Compare two plans
     */
    comparePlans(plan1: PricingPlan, plan2: PricingPlan): {
        priceDifference: number;
        featureDifference: string[];
        isUpgrade: boolean;
    } {
        const priceDifference = plan2.price - plan1.price;
        const features1 = this.getPlanFeatures(plan1);
        const features2 = this.getPlanFeatures(plan2);
        
        const featureDifference = features2.filter(f => !features1.includes(f));
        const isUpgrade = priceDifference > 0;

        return {
            priceDifference,
            featureDifference,
            isUpgrade,
        };
    }

    /**
     * Map Prisma PricingPlan to GraphQL entity
     */
    private mapPricingPlanToEntity(plan: PrismaPricingPlan): PricingPlan {
        return {
            id: plan.id,
            name: plan.name,
            description: plan.description,
            price: plan.price,
            currency: plan.currency,
            billingPeriod: plan.billingPeriod,
            features: plan.features as string[],
            maxJobAdverts: plan.maxJobAdverts,
            maxUsers: plan.maxUsers,
            maxCandidates: plan.maxCandidates,
            unlimitedJobAdverts: plan.unlimitedJobAdverts,
            unlimitedUsers: plan.unlimitedUsers,
            unlimitedCandidates: plan.unlimitedCandidates,
            hasAnalytics: plan.hasAnalytics,
            hasPrioritySupport: plan.hasPrioritySupport,
            hasCustomBranding: plan.hasCustomBranding,
            hasApiAccess: plan.hasApiAccess,
            planType: plan.planType,
            subscriptionModel: plan.subscriptionModel,
            displayOrder: plan.displayOrder,
            isActive: plan.isActive,
            isFeatured: plan.isFeatured,
            stripeProductId: plan.stripeProductId,
            stripePriceId: plan.stripePriceId,
            createdAt: plan.createdAt,
            updatedAt: plan.updatedAt
        };
    }
}
