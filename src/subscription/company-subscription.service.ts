import {
    Injectable,
    NotFoundException,
    BadRequestException,
} from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { StripeService } from '../stripe/stripe.service'
import { StripeDynamicProductService } from '../stripe/stripe-dynamic-product.service'
import { PricingPlanService } from './pricing-plan.service'
import { ConfigService } from '@nestjs/config'
import {
    Company,
    StripeSubscription,
    CompanySubscriptionUsage,
    SubscriptionStatus,
    SubscriptionModel,
    BillingPeriod,
    SubscriptionType,
} from '@prisma/client'
import { CreateCompanyCheckoutSessionInput } from './dto/create-company-checkout-session.input'
import Stripe from 'stripe'

@Injectable()
export class CompanySubscriptionService {
    private stripe: Stripe

    constructor(
        private readonly prismaService: PrismaService,
        private readonly stripeService: StripeService,
        private readonly stripeDynamicProductService: StripeDynamicProductService,
        private readonly pricingPlanService: PricingPlanService,
        private readonly configService: ConfigService
    ) {
        this.stripe = this.stripeService.getStripe()
    }

    /**
     * Create a Stripe checkout session for company subscription
     */
    async createCheckoutSession(
        input: CreateCompanyCheckoutSessionInput
    ): Promise<string> {
        // Get the company
        const company = await this.prismaService.company.findUnique({
            where: { id: input.companyId },
            include: { stripeCustomer: true },
        })

        if (!company) {
            throw new NotFoundException(
                `Company with ID ${input.companyId} not found`
            )
        }

        // Get the pricing plan
        const plan = await this.pricingPlanService.getPlanById(input.planId)

        if (!plan || !plan.isActive) {
            throw new BadRequestException('Invalid or inactive pricing plan')
        }

        // Create or get Stripe customer
        let stripeCustomerId = company.stripeCustomerId

        if (!stripeCustomerId) {
            const stripeCustomer = await this.stripe.customers.create({
                email: company.contactEmail || undefined,
                name: company.name,
                metadata: {
                    companyId: company.id,
                    companyName: company.name,
                },
            })

            stripeCustomerId = stripeCustomer.id

            // Update company with Stripe customer ID
            await this.prismaService.company.update({
                where: { id: company.id },
                data: { stripeCustomerId },
            })
        }

        // Prepare checkout session parameters
        const sessionParams: Stripe.Checkout.SessionCreateParams = {
            customer: stripeCustomerId,
            payment_method_types: ['card'],
            mode:
                plan.billingPeriod === BillingPeriod.ONE_TIME
                    ? 'payment'
                    : 'subscription',
            success_url: input.successUrl,
            cancel_url: input.cancelUrl,
            metadata: {
                companyId: company.id,
                planId: plan.id,
                subscriptionModel: SubscriptionModel.COMPANY,
            },
        }

        // Handle dynamic product creation if no Stripe price exists
        let stripePriceId = plan.stripePriceId

        if (!stripePriceId) {
            // Create product and price on the fly
            const stripeProduct =
                await this.stripeDynamicProductService.createProduct({
                    name: `${plan.name} - ${company.name}`,
                    description: plan.description || undefined,
                    metadata: {
                        planId: plan.id,
                        companyId: company.id,
                        billingPeriod: plan.billingPeriod,
                    },
                })

            const stripePrice =
                await this.stripeDynamicProductService.createPrice({
                    productId: stripeProduct.id,
                    unitAmount: Math.round(plan.price * 100),
                    currency: plan.currency.toLowerCase(),
                    billingPeriod: plan.billingPeriod,
                })

            stripePriceId = stripePrice.id
        }

        // Add line items
        sessionParams.line_items = [
            {
                price: stripePriceId,
                quantity: 1,
            },
        ]

        // Apply coupon if provided
        if (input.couponCode) {
            try {
                const coupon = await this.stripe.coupons.retrieve(
                    input.couponCode
                )
                if (coupon && coupon.valid) {
                    sessionParams.discounts = [{ coupon: input.couponCode }]
                }
            } catch (error) {
                console.warn(`Invalid coupon code: ${input.couponCode}`)
            }
        }

        // Create the checkout session
        const session =
            await this.stripe.checkout.sessions.create(sessionParams)

        // Store pending subscription info
        await this.prismaService.stripeSubscription.create({
            data: {
                stripeSubscriptionId: session.id,
                stripeCustomerId: stripeCustomerId,
                stripePriceId: stripePriceId,
                status: 'pending',
                currentPeriodStart: new Date(),
                currentPeriodEnd: new Date(),
                subscriptionModel: SubscriptionModel.COMPANY,
                companyId: company.id,
                pricingPlanId: plan.id,
                subscriptionType: SubscriptionType.COMPANY_UNLIMITED,
                metadata: {
                    checkoutSessionId: session.id,
                    planName: plan.name,
                },
            },
        })

        return session.url || ''
    }

    /**
     * Handle successful checkout session completion
     */
    async handleCheckoutSessionCompleted(
        session: Stripe.Checkout.Session
    ): Promise<void> {
        const companyId = session.metadata?.companyId
        const planId = session.metadata?.planId

        if (!companyId || !planId) {
            console.error('Missing metadata in checkout session')
            return
        }

        // Get the pricing plan
        const plan = await this.pricingPlanService.getPlanById(planId)

        // Update company subscription status
        await this.prismaService.company.update({
            where: { id: companyId },
            data: {
                subscriptionStatus: SubscriptionStatus.ACTIVE,
                currentPlanId: planId,
                subscriptionStartDate: new Date(),
                subscriptionEndDate: this.calculateEndDate(plan.billingPeriod),
                stripeCustomerId: session.customer as string,
            },
        })

        // Create or update subscription usage tracking
        await this.prismaService.companySubscriptionUsage.upsert({
            where: { companyId },
            create: {
                companyId,
                currentJobAdverts: 0,
                maxJobAdverts: plan.maxJobAdverts,
                currentUsers: 0,
                maxUsers: plan.maxUsers,
                currentCandidates: 0,
                maxCandidates: plan.maxCandidates,
                billingPeriodStart: new Date(),
                billingPeriodEnd: this.calculateEndDate(plan.billingPeriod),
            },
            update: {
                maxJobAdverts: plan.maxJobAdverts,
                maxUsers: plan.maxUsers,
                maxCandidates: plan.maxCandidates,
                billingPeriodStart: new Date(),
                billingPeriodEnd: this.calculateEndDate(plan.billingPeriod),
            },
        })

        // Update Stripe subscription record
        if (session.subscription) {
            await this.prismaService.stripeSubscription.updateMany({
                where: {
                    companyId,
                    stripeSubscriptionId: session.id,
                },
                data: {
                    stripeSubscriptionId: session.subscription as string,
                    status: 'active',
                    currentPeriodStart: new Date(),
                    currentPeriodEnd: this.calculateEndDate(plan.billingPeriod),
                },
            })
        }
    }

    /**
     * Get company subscription status
     */
    async getCompanySubscriptionStatus(companyId: string): Promise<any> {
        const company = await this.prismaService.company.findUnique({
            where: { id: companyId },
            include: {
                currentPlan: true,
                subscriptionUsage: true,
                subscriptions: {
                    where: { status: 'active' },
                    orderBy: { createdAt: 'desc' },
                    take: 1,
                },
            },
        })

        if (!company) {
            throw new NotFoundException(
                `Company with ID ${companyId} not found`
            )
        }

        return {
            company,
            subscriptionStatus: company.subscriptionStatus,
            currentPlan: company.currentPlan,
            usage: company.subscriptionUsage,
            activeSubscription: company.subscriptions[0] || null,
            subscriptionEndDate: company.subscriptionEndDate,
            daysRemaining: this.calculateDaysRemaining(
                company.subscriptionEndDate
            ),
        }
    }

    /**
     * Check if company can create a new job advert
     */
    async canCreateJobAdvert(companyId: string): Promise<boolean> {
        const usage =
            await this.prismaService.companySubscriptionUsage.findUnique({
                where: { companyId },
                include: { company: { include: { currentPlan: true } } },
            })

        if (!usage || !usage.company.currentPlan) {
            return false
        }

        const plan = usage.company.currentPlan

        // Check subscription status
        if (usage.company.subscriptionStatus !== SubscriptionStatus.ACTIVE) {
            return false
        }

        // Check expiration
        if (
            usage.company.subscriptionEndDate &&
            usage.company.subscriptionEndDate < new Date()
        ) {
            return false
        }

        // Check job advert limit
        if (plan.unlimitedJobAdverts) {
            return true
        }

        return usage.currentJobAdverts < (usage.maxJobAdverts || 0)
    }

    /**
     * Increment job advert usage
     */
    async incrementJobAdvertUsage(companyId: string): Promise<void> {
        const canCreate = await this.canCreateJobAdvert(companyId)

        if (!canCreate) {
            throw new BadRequestException(
                'Job advert limit reached or subscription inactive'
            )
        }

        await this.prismaService.companySubscriptionUsage.update({
            where: { companyId },
            data: {
                currentJobAdverts: { increment: 1 },
                lastUsageUpdate: new Date(),
            },
        })
    }

    /**
     * Cancel company subscription
     */
    async cancelSubscription(companyId: string): Promise<void> {
        const company = await this.prismaService.company.findUnique({
            where: { id: companyId },
            include: {
                subscriptions: {
                    where: { status: 'active' },
                    orderBy: { createdAt: 'desc' },
                    take: 1,
                },
            },
        })

        if (!company) {
            throw new NotFoundException(
                `Company with ID ${companyId} not found`
            )
        }

        const activeSubscription = company.subscriptions[0]

        if (activeSubscription && activeSubscription.stripeSubscriptionId) {
            // Cancel in Stripe
            try {
                await this.stripe.subscriptions.update(
                    activeSubscription.stripeSubscriptionId,
                    {
                        cancel_at_period_end: true,
                    }
                )
            } catch (error) {
                console.error('Error canceling Stripe subscription:', error)
            }

            // Update database
            await this.prismaService.stripeSubscription.update({
                where: { id: activeSubscription.id },
                data: {
                    status: 'canceled',
                    canceledAt: new Date(),
                },
            })
        }

        // Update company subscription status
        await this.prismaService.company.update({
            where: { id: companyId },
            data: {
                subscriptionStatus: SubscriptionStatus.CANCELED,
            },
        })
    }

    /**
     * Upgrade or downgrade subscription plan
     */
    async changePlan(companyId: string, newPlanId: string): Promise<string> {
        const company = await this.prismaService.company.findUnique({
            where: { id: companyId },
            include: {
                currentPlan: true,
                subscriptions: {
                    where: { status: 'active' },
                    orderBy: { createdAt: 'desc' },
                    take: 1,
                },
            },
        })

        if (!company) {
            throw new NotFoundException(
                `Company with ID ${companyId} not found`
            )
        }

        const newPlan = await this.pricingPlanService.getPlanById(newPlanId)

        if (!newPlan || !newPlan.isActive) {
            throw new BadRequestException('Invalid or inactive pricing plan')
        }

        // If no active subscription, create a new checkout session
        if (!company.subscriptions[0]) {
            return this.createCheckoutSession({
                companyId,
                planId: newPlanId,
                successUrl:
                    this.configService.get('FRONTEND_URL') +
                    '/subscription/success',
                cancelUrl:
                    this.configService.get('FRONTEND_URL') +
                    '/subscription/cancel',
            })
        }

        // Update existing subscription
        const activeSubscription = company.subscriptions[0]

        // Create new price if needed
        let stripePriceId = newPlan.stripePriceId
        if (!stripePriceId) {
            const stripeProduct =
                await this.stripeDynamicProductService.createProduct({
                    name: `${newPlan.name} - ${company.name}`,
                    description: newPlan.description || undefined,
                    metadata: {
                        planId: newPlan.id,
                        companyId: company.id,
                    },
                })

            const stripePrice =
                await this.stripeDynamicProductService.createPrice({
                    productId: stripeProduct.id,
                    unitAmount: Math.round(newPlan.price * 100),
                    currency: newPlan.currency.toLowerCase(),
                    billingPeriod: newPlan.billingPeriod,
                })

            stripePriceId = stripePrice.id
        }

        // Update subscription in Stripe
        await this.stripe.subscriptions.update(
            activeSubscription.stripeSubscriptionId,
            {
                items: [
                    {
                        id: activeSubscription.stripeSubscriptionId,
                        price: stripePriceId,
                    },
                ],
                proration_behavior: 'create_prorations',
            }
        )

        // Update database
        await this.prismaService.company.update({
            where: { id: companyId },
            data: {
                currentPlanId: newPlanId,
            },
        })

        await this.prismaService.stripeSubscription.update({
            where: { id: activeSubscription.id },
            data: {
                pricingPlanId: newPlanId,
                stripePriceId,
            },
        })

        return 'Plan updated successfully'
    }

    /**
     * Reset usage counters at the start of a new billing period
     */
    async resetBillingPeriodUsage(companyId: string): Promise<void> {
        const usage =
            await this.prismaService.companySubscriptionUsage.findUnique({
                where: { companyId },
                include: { company: { include: { currentPlan: true } } },
            })

        if (!usage || !usage.company.currentPlan) {
            return
        }

        const plan = usage.company.currentPlan
        const now = new Date()

        // Check if billing period has ended
        if (usage.billingPeriodEnd && usage.billingPeriodEnd < now) {
            await this.prismaService.companySubscriptionUsage.update({
                where: { companyId },
                data: {
                    currentJobAdverts: 0,
                    currentUsers: 0,
                    currentCandidates: 0,
                    billingPeriodStart: now,
                    billingPeriodEnd: this.calculateEndDate(plan.billingPeriod),
                    lastUsageUpdate: now,
                },
            })
        }
    }

    /**
     * Get subscription metrics for admin dashboard
     */
    async getSubscriptionMetrics(): Promise<any> {
        const [
            totalCompanies,
            activeSubscriptions,
            canceledSubscriptions,
            trialSubscriptions,
            revenueByPlan,
        ] = await Promise.all([
            this.prismaService.company.count(),
            this.prismaService.company.count({
                where: { subscriptionStatus: SubscriptionStatus.ACTIVE },
            }),
            this.prismaService.company.count({
                where: { subscriptionStatus: SubscriptionStatus.CANCELED },
            }),
            this.prismaService.company.count({
                where: { subscriptionStatus: SubscriptionStatus.TRIAL },
            }),
            this.prismaService.stripeSubscription.groupBy({
                by: ['pricingPlanId'],
                where: { status: 'active' },
                _count: true,
            }),
        ])

        return {
            totalCompanies,
            activeSubscriptions,
            canceledSubscriptions,
            trialSubscriptions,
            conversionRate:
                totalCompanies > 0
                    ? (activeSubscriptions / totalCompanies) * 100
                    : 0,
            revenueByPlan,
        }
    }

    /**
     * Helper: Calculate subscription end date based on billing period
     */
    private calculateEndDate(billingPeriod: BillingPeriod): Date {
        const now = new Date()

        switch (billingPeriod) {
            case BillingPeriod.MONTHLY:
                return new Date(now.setMonth(now.getMonth() + 1))
            case BillingPeriod.QUARTERLY:
                return new Date(now.setMonth(now.getMonth() + 3))
            case BillingPeriod.SEMI_ANNUAL:
                return new Date(now.setMonth(now.getMonth() + 6))
            case BillingPeriod.ANNUAL:
                return new Date(now.setFullYear(now.getFullYear() + 1))
            case BillingPeriod.ONE_TIME:
                // For one-time payments, set end date to far future
                return new Date(now.setFullYear(now.getFullYear() + 100))
            default:
                return new Date(now.setMonth(now.getMonth() + 1))
        }
    }

    /**
     * Helper: Calculate days remaining in subscription
     */
    private calculateDaysRemaining(endDate: Date | null): number {
        if (!endDate) return 0

        const now = new Date()
        const diff = endDate.getTime() - now.getTime()

        return Math.max(0, Math.ceil(diff / (1000 * 60 * 60 * 24)))
    }
}
