import { InputType, Field, Int, registerEnumType } from '@nestjs/graphql';
import { BillingPeriod, SubscriptionModel, PricingPlanType } from '@prisma/client';
import GraphQLJSON from 'graphql-type-json';

registerEnumType(PricingPlanType, {
    name: 'PricingPlanType',
});

registerEnumType(SubscriptionModel, {
    name: 'SubscriptionModel',
});

registerEnumType(BillingPeriod, {
    name: 'BillingPeriod',
});
import { 
    IsString, 
    IsInt, 
    IsBoolean, 
    IsOptional, 
    IsEnum, 
    IsObject,
    Min,
    MaxLength
} from 'class-validator';

@InputType()
export class CreatePricingPlanInput {
    @Field()
    @IsString()
    @MaxLength(100)
    name: string;

    @Field()
    @IsString()
    @MaxLength(100)
    displayName: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    @MaxLength(500)
    description?: string;

    @Field(() => GraphQLJSON)
    @IsObject()
    features: any;

    @Field(() => Int)
    @IsInt()
    @Min(0)
    priceInCents: number;

    @Field({ defaultValue: 'EUR' })
    @IsString()
    @MaxLength(3)
    currency: string;

    @Field(() => BillingPeriod)
    @IsEnum(BillingPeriod)
    billingPeriod: BillingPeriod;

    @Field(() => Int)
    @IsInt()
    @Min(1)
    durationDays: number;

    @Field({ defaultValue: false })
    @IsBoolean()
    isPopular: boolean;

    @Field({ defaultValue: false })
    @IsBoolean()
    isCustom: boolean;

    @Field(() => Int, { defaultValue: 0 })
    @IsInt()
    @Min(0)
    sortOrder: number;

    @Field({ defaultValue: true })
    @IsBoolean()
    isActive: boolean;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsInt()
    @Min(-1)
    maxJobAdverts?: number;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsInt()
    @Min(-1)
    maxUsers?: number;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsInt()
    @Min(-1)
    maxCandidates?: number;

    @Field({ nullable: true })
    @IsOptional()
    @IsBoolean()
    unlimitedJobAdverts?: boolean;

    @Field({ nullable: true })
    @IsOptional()
    @IsBoolean()
    unlimitedUsers?: boolean;

    @Field({ nullable: true })
    @IsOptional()
    @IsBoolean()
    unlimitedCandidates?: boolean;

    @Field({ nullable: true })
    @IsOptional()
    @IsBoolean()
    hasAnalytics?: boolean;

    @Field({ nullable: true })
    @IsOptional()
    @IsBoolean()
    hasPrioritySupport?: boolean;

    @Field({ nullable: true })
    @IsOptional()
    @IsBoolean()
    hasCustomBranding?: boolean;

    @Field({ nullable: true })
    @IsOptional()
    @IsBoolean()
    hasApiAccess?: boolean;

    @Field(() => PricingPlanType, { nullable: true })
    @IsOptional()
    @IsEnum(PricingPlanType)
    planType?: PricingPlanType;

    @Field(() => SubscriptionModel, { nullable: true })
    @IsOptional()
    @IsEnum(SubscriptionModel)
    subscriptionModel?: SubscriptionModel;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsInt()
    @Min(0)
    displayOrder?: number;

    @Field({ nullable: true })
    @IsOptional()
    @IsBoolean()
    isFeatured?: boolean;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsInt()
    @Min(0)
    price?: number;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    @MaxLength(50)
    supportLevel?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsBoolean()
    createStripeProduct?: boolean;
}

@InputType()
export class UpdatePricingPlanInput {
    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    @MaxLength(100)
    displayName?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    @MaxLength(500)
    description?: string;

    @Field(() => GraphQLJSON, { nullable: true })
    @IsOptional()
    @IsObject()
    features?: any;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsInt()
    @Min(0)
    priceInCents?: number;

    @Field({ nullable: true })
    @IsOptional()
    @IsBoolean()
    isPopular?: boolean;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsInt()
    @Min(0)
    sortOrder?: number;

    @Field({ nullable: true })
    @IsOptional()
    @IsBoolean()
    isActive?: boolean;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    @MaxLength(50)
    supportLevel?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    @MaxLength(100)
    name?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    @MaxLength(3)
    currency?: string;

    @Field(() => BillingPeriod, { nullable: true })
    @IsOptional()
    @IsEnum(BillingPeriod)
    billingPeriod?: BillingPeriod;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsInt()
    @Min(0)
    price?: number;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsInt()
    @Min(-1)
    maxJobAdverts?: number;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsInt()
    @Min(-1)
    maxUsers?: number;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsInt()
    @Min(-1)
    maxCandidates?: number;

    @Field({ nullable: true })
    @IsOptional()
    @IsBoolean()
    unlimitedJobAdverts?: boolean;

    @Field({ nullable: true })
    @IsOptional()
    @IsBoolean()
    unlimitedUsers?: boolean;

    @Field({ nullable: true })
    @IsOptional()
    @IsBoolean()
    unlimitedCandidates?: boolean;

    @Field({ nullable: true })
    @IsOptional()
    @IsBoolean()
    hasAnalytics?: boolean;

    @Field({ nullable: true })
    @IsOptional()
    @IsBoolean()
    hasPrioritySupport?: boolean;

    @Field({ nullable: true })
    @IsOptional()
    @IsBoolean()
    hasCustomBranding?: boolean;

    @Field({ nullable: true })
    @IsOptional()
    @IsBoolean()
    hasApiAccess?: boolean;

    @Field(() => PricingPlanType, { nullable: true })
    @IsOptional()
    @IsEnum(PricingPlanType)
    planType?: PricingPlanType;

    @Field(() => SubscriptionModel, { nullable: true })
    @IsOptional()
    @IsEnum(SubscriptionModel)
    subscriptionModel?: SubscriptionModel;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsInt()
    @Min(0)
    displayOrder?: number;

    @Field({ nullable: true })
    @IsOptional()
    @IsBoolean()
    isFeatured?: boolean;
}
