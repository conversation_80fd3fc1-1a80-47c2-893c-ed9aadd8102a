import { InputType, Field, Int } from '@nestjs/graphql';
import { BillingPeriod } from '@prisma/client';
import GraphQLJSON from 'graphql-type-json';
import { 
    IsString, 
    IsInt, 
    IsBoolean, 
    IsOptional, 
    IsEnum, 
    IsObject,
    Min,
    MaxLength
} from 'class-validator';

@InputType()
export class CreatePricingPlanInput {
    @Field()
    @IsString()
    @MaxLength(100)
    name: string;

    @Field()
    @IsString()
    @MaxLength(100)
    displayName: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    @MaxLength(500)
    description?: string;

    @Field(() => GraphQLJSON)
    @IsObject()
    features: any;

    @Field(() => Int)
    @IsInt()
    @Min(0)
    priceInCents: number;

    @Field({ defaultValue: 'EUR' })
    @IsString()
    @MaxLength(3)
    currency: string;

    @Field(() => BillingPeriod)
    @IsEnum(BillingPeriod)
    billingPeriod: BillingPeriod;

    @Field(() => Int)
    @IsInt()
    @Min(1)
    durationDays: number;

    @Field({ defaultValue: false })
    @IsBoolean()
    isPopular: boolean;

    @Field({ defaultValue: false })
    @IsBoolean()
    isCustom: boolean;

    @Field(() => Int, { defaultValue: 0 })
    @IsInt()
    @Min(0)
    sortOrder: number;

    @Field({ defaultValue: true })
    @IsBoolean()
    isActive: boolean;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsInt()
    @Min(-1)
    maxJobAdverts?: number;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    @MaxLength(50)
    supportLevel?: string;
}

@InputType()
export class UpdatePricingPlanInput {
    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    @MaxLength(100)
    displayName?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    @MaxLength(500)
    description?: string;

    @Field(() => GraphQLJSON, { nullable: true })
    @IsOptional()
    @IsObject()
    features?: any;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsInt()
    @Min(0)
    priceInCents?: number;

    @Field({ nullable: true })
    @IsOptional()
    @IsBoolean()
    isPopular?: boolean;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsInt()
    @Min(0)
    sortOrder?: number;

    @Field({ nullable: true })
    @IsOptional()
    @IsBoolean()
    isActive?: boolean;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    @MaxLength(50)
    supportLevel?: string;
}
