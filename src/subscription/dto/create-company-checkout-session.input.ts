import { InputType, Field, ID } from '@nestjs/graphql';
import { IsString, IsUUID, IsOptional, IsUrl } from 'class-validator';

@InputType()
export class CreateCompanyCheckoutSessionInput {
    @Field(() => ID)
    @IsUUID()
    planId: string;

    @Field(() => ID)
    @IsUUID()
    companyId: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    couponCode?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsUrl()
    successUrl?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsUrl()
    cancelUrl?: string;
}
