import { InputType, Field } from '@nestjs/graphql';
import { IsString, IsUUID, IsOptional, IsUrl } from 'class-validator';

@InputType()
export class CreateCompanyCheckoutSessionInput {
    @Field()
    @IsUUID()
    planId: string;

    @Field()
    @IsUUID()
    companyId: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    couponCode?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsUrl()
    successUrl?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsUrl()
    cancelUrl?: string;
}
