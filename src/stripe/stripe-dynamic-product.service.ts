import { Injectable, Logger } from '@nestjs/common';
import { StripeService } from './stripe.service';
import Stripe from 'stripe';
import { Company, PricingPlan, BillingPeriod } from '@prisma/client';

@Injectable()
export class StripeDynamicProductService {
    private readonly logger = new Logger(StripeDynamicProductService.name);
    private stripe: Stripe;

    constructor(private stripeService: StripeService) {
        this.stripe = this.stripeService.getStripe();
    }

    /**
     * Create a Stripe product for a company
     */
    async createProductForCompany(
        company: Company,
        plan: PricingPlan
    ): Promise<Stripe.Product> {
        try {
            const product = await this.stripe.products.create({
                name: `${company.name} - ${plan.displayName}`,
                description: plan.description || undefined,
                metadata: {
                    companyId: company.id,
                    companyName: company.name,
                    planId: plan.id,
                    planName: plan.name,
                },
            });

            this.logger.log(
                `Created Stripe product ${product.id} for company ${company.name}`
            );
            return product;
        } catch (error) {
            this.logger.error(
                `Failed to create Stripe product for company ${company.name}`,
                error
            );
            throw error;
        }
    }

    /**
     * Create a Stripe price for a product
     */
    async createPriceForProduct(
        productId: string,
        priceInCents: number,
        billingPeriod: BillingPeriod,
        currency: string = 'eur'
    ): Promise<Stripe.Price> {
        try {
            const interval = this.mapBillingPeriodToStripeInterval(billingPeriod);
            
            const priceData: Stripe.PriceCreateParams = {
                product: productId,
                unit_amount: priceInCents,
                currency: currency.toLowerCase(),
            };

            // Add recurring configuration if not custom
            if (billingPeriod !== BillingPeriod.CUSTOM) {
                priceData.recurring = {
                    interval: interval.interval,
                    interval_count: interval.interval_count,
                };
            }

            const price = await this.stripe.prices.create(priceData);

            this.logger.log(
                `Created Stripe price ${price.id} for product ${productId}`
            );
            return price;
        } catch (error) {
            this.logger.error(
                `Failed to create Stripe price for product ${productId}`,
                error
            );
            throw error;
        }
    }

    /**
     * Get or create a Stripe product and price for a company and plan
     */
    async getOrCreateProductAndPrice(
        company: Company,
        plan: PricingPlan,
        customPriceInCents?: number
    ): Promise<{ product: Stripe.Product; price: Stripe.Price }> {
        try {
            // Check if we already have a product for this company and plan
            const existingProducts = await this.stripe.products.search({
                query: `metadata['companyId']:'${company.id}' AND metadata['planId']:'${plan.id}'`,
            });

            let product: Stripe.Product;
            
            if (existingProducts.data.length > 0) {
                product = existingProducts.data[0];
                this.logger.log(
                    `Using existing Stripe product ${product.id} for company ${company.name}`
                );
            } else {
                product = await this.createProductForCompany(company, plan);
            }

            // Create a new price (prices are immutable, so we always create new ones)
            const priceAmount = customPriceInCents || plan.priceInCents;
            const price = await this.createPriceForProduct(
                product.id,
                priceAmount,
                plan.billingPeriod,
                plan.currency
            );

            return { product, price };
        } catch (error) {
            this.logger.error(
                `Failed to get or create product and price for company ${company.name}`,
                error
            );
            throw error;
        }
    }

    /**
     * Archive a Stripe product (we don't delete, just deactivate)
     */
    async archiveProduct(productId: string): Promise<void> {
        try {
            await this.stripe.products.update(productId, {
                active: false,
            });
            this.logger.log(`Archived Stripe product ${productId}`);
        } catch (error) {
            this.logger.error(`Failed to archive Stripe product ${productId}`, error);
            throw error;
        }
    }

    /**
     * Archive a Stripe price (we don't delete, just deactivate)
     */
    async archivePrice(priceId: string): Promise<void> {
        try {
            await this.stripe.prices.update(priceId, {
                active: false,
            });
            this.logger.log(`Archived Stripe price ${priceId}`);
        } catch (error) {
            this.logger.error(`Failed to archive Stripe price ${priceId}`, error);
            throw error;
        }
    }

    /**
     * Map our BillingPeriod enum to Stripe's interval format
     */
    private mapBillingPeriodToStripeInterval(
        billingPeriod: BillingPeriod
    ): { interval: Stripe.PriceCreateParams.Recurring.Interval; interval_count: number } {
        switch (billingPeriod) {
            case BillingPeriod.MONTHLY:
                return { interval: 'month', interval_count: 1 };
            case BillingPeriod.QUARTERLY:
                return { interval: 'month', interval_count: 3 };
            case BillingPeriod.SEMI_ANNUAL:
                return { interval: 'month', interval_count: 6 };
            case BillingPeriod.ANNUAL:
                return { interval: 'year', interval_count: 1 };
            case BillingPeriod.CUSTOM:
                // For custom, we'll default to monthly but this should be overridden
                return { interval: 'month', interval_count: 1 };
            default:
                return { interval: 'month', interval_count: 1 };
        }
    }

    /**
     * Create a generic Stripe product
     */
    async createProduct(params: {
        name: string;
        description?: string;
        metadata?: Record<string, string>;
    }): Promise<Stripe.Product> {
        try {
            const product = await this.stripe.products.create({
                name: params.name,
                description: params.description,
                metadata: params.metadata || {},
            });

            this.logger.log(`Created Stripe product ${product.id}`);
            return product;
        } catch (error) {
            this.logger.error('Failed to create Stripe product', error);
            throw error;
        }
    }

    /**
     * Create a generic Stripe price
     */
    async createPrice(params: {
        productId: string;
        unitAmount: number;
        currency: string;
        billingPeriod: BillingPeriod;
    }): Promise<Stripe.Price> {
        return this.createPriceForProduct(
            params.productId,
            params.unitAmount,
            params.billingPeriod,
            params.currency
        );
    }

    /**
     * Create a one-time price for custom billing periods
     */
    async createOneTimePrice(
        productId: string,
        priceInCents: number,
        currency: string = 'eur'
    ): Promise<Stripe.Price> {
        try {
            const price = await this.stripe.prices.create({
                product: productId,
                unit_amount: priceInCents,
                currency: currency.toLowerCase(),
                // No recurring configuration for one-time payments
            });

            this.logger.log(
                `Created one-time Stripe price ${price.id} for product ${productId}`
            );
            return price;
        } catch (error) {
            this.logger.error(
                `Failed to create one-time Stripe price for product ${productId}`,
                error
            );
            throw error;
        }
    }
}
