import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { StripeModule as GolevelupStripeModule } from '@golevelup/nestjs-stripe';
import { StripeService } from './stripe.service';
import { StripeDynamicProductService } from './stripe-dynamic-product.service';

@Module({
    imports: [
        GolevelupStripeModule.forRootAsync(GolevelupStripeModule, {
            imports: [ConfigModule],
            useFactory: (configService: ConfigService) =>
                configService.get('STRIPE_CONFIG'),
            inject: [ConfigService],
        }),
    ],
    providers: [StripeService, StripeDynamicProductService],
    exports: [StripeService, StripeDynamicProductService],
})
export class StripeModule {}
