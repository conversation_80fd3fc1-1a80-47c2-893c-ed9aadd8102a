import { Injectable, NotFoundException } from '@nestjs/common'
import { UpdateJobAdvertInput } from './dto/update-job-advert.input'
import { JobAdvert } from '@prisma/client'
import { PrismaService } from '../prisma.service'
import { JobAdsFilterOptions } from './types'
import { CreateJobAdvertInput } from './dto/create-job-advert.input'
import { JobAdvertStats } from './entities/job-advert-stats'
import { EmailServerService } from '../email-server/email-server.service'
import { PusherPubSubService } from '../pub-sub/pusher-pub-sub.service'
import { PaginationInput } from '../common/dto/pagination.input'
import { IPaginatedType } from '../common/dto/pagination-response'

@Injectable()
export class JobAdvertService {
    constructor(
        private readonly prisma: PrismaService,
        private readonly emailService: EmailServerService,
        private readonly pusherService: PusherPubSubService
    ) {}

    jobAdvertInclude = {
        company: true,
        responsibleUsers: true,
        jobAction: true,
        subscriptions: true,
        categories: true,
    }

    async create(
        createJobAdvertInput: CreateJobAdvertInput,
        categoryIds: string[],
        companyId: string,
        responsibleUsersIds?: string[]
    ): Promise<JobAdvert> {
        const { id, ...jobAdvertData } = createJobAdvertInput as any
        const newJob = await this.prisma.jobAdvert.create({
            data: {
                ...jobAdvertData,
                isDraft: true,
                responsibleUsers:
                    responsibleUsersIds.length > 0
                        ? {
                              connect: responsibleUsersIds.map((id) => ({
                                  id,
                              })),
                          }
                        : undefined,
                categories: {
                    connect: categoryIds.map((id) => ({ id })),
                },
                company: {
                    connect: {
                        id: companyId,
                    },
                },
            },
            include: this.jobAdvertInclude,
        })

        await this.emailService.sendNewJobAdEmail({
            jobAdId: newJob.id as string,
            companyId: companyId as string,
            jobAdTitle: newJob.title as string,
        })

        await this.publishJobAdvertEvent(newJob)

        return newJob
    }

    findAll() {
        return this.prisma.jobAdvert.findMany({
            where: {
                isDraft: false,
            },
            include: this.jobAdvertInclude,
        })
    }

    async findPaginated(
        paginationInput: PaginationInput
    ): Promise<IPaginatedType<JobAdvert>> {
        const {
            page = 1,
            limit = 10,
            search = '',
            status = '',
        } = paginationInput
        const skip = (page - 1) * limit

        // Build the where condition based on search and status
        let where: any = {
            isDraft: false,
        }

        // Add search condition if search string is provided
        if (search) {
            where = {
                ...where,
                OR: [
                    {
                        title: {
                            contains: search,
                            mode: 'insensitive' as const,
                        },
                    },
                    {
                        district: {
                            contains: search,
                            mode: 'insensitive' as const,
                        },
                    },
                    {
                        city: {
                            contains: search,
                            mode: 'insensitive' as const,
                        },
                    },
                    {
                        description: {
                            contains: search,
                            mode: 'insensitive' as const,
                        },
                    },
                ],
            }
        }

        // Add status filtering if status is provided
        if (status) {
            switch (status) {
                case 'Approved':
                    where.approved = true
                    break
                case 'Blocked':
                    where.isDeclined = true
                    break
                case 'Pending':
                    where.approved = false
                    where.isDeclined = false
                    break
                // 'All' case doesn't need additional filters
            }
        }

        const [items, totalItems] = await Promise.all([
            this.prisma.jobAdvert.findMany({
                where,
                include: this.jobAdvertInclude,
                skip,
                take: limit,
                orderBy: {
                    createdAt: 'desc',
                },
            }),
            this.prisma.jobAdvert.count({
                where,
            }),
        ])

        const totalPages = Math.ceil(totalItems / limit)

        return {
            items,
            meta: {
                totalItems,
                itemCount: items.length,
                itemsPerPage: limit,
                totalPages,
                currentPage: page,
            },
        }
    }

    async findByCompany(companyId: string, companyUserId: string) {
        const jobAds = this.prisma.jobAdvert.findMany({
            where: {
                companyId: companyId,
                isDeleted: false,
                responsibleUsers: {
                    some: {
                        id: companyUserId,
                    },
                },
            },
            include: {
                jobAction: true,
                subscriptions: true,
                company: true,
                responsibleUsers: true,
                _count: {
                    select: {
                        jobAction: {
                            where: {
                                state: 'LIKED',
                            },
                        },
                    },
                },
            },
        })

        return {
            ...jobAds,
            _count: { views: 0, jobApplications: 0, bookmarks: 0, matches: 0 },
        }
    }
    async findCompanyJobAds(companyId: string) {
        const jobAds = this.prisma.jobAdvert.findMany({
            where: {
                companyId: companyId,
                isDeleted: false,
                isDeclined: false,
                approved: true,
            },
        })

        return {
            ...jobAds,
        }
    }

    async filteredJobAdverts(
        applicantId: string = null,
        includeDisliked: boolean = false,
        filterOptions: JobAdsFilterOptions
    ) {
        let applicantFilter: any = {}

        if (applicantId) {
            applicantFilter = await this.prisma.jobsFilter.findUnique({
                where: {
                    applicantId: applicantId,
                },
                include: {
                    categories: true,
                },
            })
        }

        const applicantCategories = (applicantFilter?.categories || []).map(
            (cat: { id: string }) => cat?.id
        )

        const combinedFilter = {
            ...filterOptions,
            ...(applicantFilter && {
                categories: applicantCategories,
                longitude: applicantFilter.longitude,
                latitude: applicantFilter.latitude,
                radius: applicantFilter.radius,
                type: applicantFilter.type,
            }),
        }

        const maxDistanceInMeters = combinedFilter?.radius * 1000 || 1000

        /**
         * ThIS `filteredJobAdverts` function fetches job adverts based on various filters.
         *
         * 1. If an `applicantId` is provided, it fetches the job filter preferences for the applicant from the database.
         * 2. It then combines the applicant's filter preferences with the `filterOptions` provided to the function.
         * 3. It constructs a SQL subquery to fetch job adverts that match the filter criteria.
         * 4. If the `includeDisliked` flag is set to true, it includes job adverts that the applicant has disliked. Otherwise, it excludes disliked job adverts.
         * 5. If a job type is provided in the combined filter, it includes only job adverts of that type.
         * 6. The subquery is then executed, and the results are returned. The results include the job advert details, company details, and categories.
         * 7. Finally, it maps over the results and formats them into a more readable format before returning them.
         *
         * If no data is returned:
         * - Check if the filters provided in `filterOptions` and the applicant's filter preferences are too restrictive.
         * - Check if the SQL subquery is constructed correctly and is able to fetch the desired job adverts.
         * - Check if the `applicantId` provided exists in the database and has associated filter preferences.
         * - Check if the job adverts in the database match the active, not paused, and approved criteria. [VERY IMPORTANT]
         */

        let subQuery = `
        SELECT "ja"."id" AS "jobAdvertId", "ja".*, 
        json_agg(json_build_object('id', "jc"."id", 'name', "jc"."name")) AS categories,
        MAX(CAST("c"."id" AS TEXT)) AS "company_id",
        MAX("c"."name") AS "company_name",
        MAX("c"."address") AS "company_address",
        MAX("c"."logoImageUrl") AS "company_logoImageUrl",
        MAX("c"."headerImageUrl") AS "company_headerImageUrl",
        MAX("c"."detailContent") AS "company_detailContent",
        MAX("c"."foundingYear") AS "company_foundingYear",
        MAX("c"."totalEmployees") AS "company_totalEmployees",
        MAX("c"."city") AS "company_city",
        ROW_NUMBER() OVER(PARTITION BY "ja"."id" ORDER BY RANDOM()) as row_number
        FROM (
            SELECT DISTINCT "ja"."id"
            FROM "JobAdvert" "ja"
            LEFT JOIN "_JobAdvertToJobCategory" "jatjc" ON "ja"."id" = "jatjc"."A"
            LEFT JOIN "JobCategory" "jc" ON "jatjc"."B" = "jc"."id"
            WHERE "ja"."paused" = false AND "ja"."approved" = true AND "ja"."isDeleted" = false AND "ja"."isDraft" = false AND "ja"."activeFromDate" <= NOW()`
        const whereConditions = []
        const params = []

        if (combinedFilter?.categories?.length > 0) {
            const placeholders = combinedFilter.categories
                .map((_, index) => `$${params.length + index + 1}::uuid`)
                .join(', ')
            whereConditions.push(`"jatjc"."B" IN (${placeholders})`)
            params.push(...combinedFilter.categories)
        }

        if (combinedFilter?.longitude && combinedFilter?.latitude) {
            const longitudeIndex = params.length + 1
            const latitudeIndex = params.length + 2
            const distanceIndex = params.length + 3
            whereConditions.push(
                `ST_DWithin(ST_MakePoint("ja".longitude, "ja".latitude)::geography, ST_MakePoint($${longitudeIndex}, $${latitudeIndex})::geography, $${distanceIndex})`
            )
            params.push(
                combinedFilter.longitude,
                combinedFilter.latitude,
                maxDistanceInMeters
            )
        }

        if (whereConditions.length > 0) {
            subQuery += ` AND ${whereConditions.join(' AND ')}`
        }

        subQuery += `
        ) filtered_ads
        LEFT JOIN "JobAdvert" "ja" ON "ja"."id" = filtered_ads."id"
        LEFT JOIN "_JobAdvertToJobCategory" "jatjc" ON "ja"."id" = "jatjc"."A"
        LEFT JOIN "JobCategory" "jc" ON "jatjc"."B" = "jc"."id"
        LEFT JOIN "Company" "c" ON "ja"."company_id" = "c"."id"
    `

        const postWhereConditions = []

        if (applicantId) {
            const applicantIdIndex = params.length + 1
            params.push(applicantId)

            subQuery += `LEFT JOIN "JobAction" "jaction" 
                ON "ja"."id" = "jaction"."job_advert_id" 
                AND "jaction"."applicant_id" = CAST($${applicantIdIndex} AS uuid) `

            if (includeDisliked) {
                postWhereConditions.push(
                    `(jaction."id" IS NULL OR jaction."state" = 'DISLIKED')`
                )
            } else {
                postWhereConditions.push(`jaction."id" IS NULL`)
            }
        }

        if (combinedFilter.type && combinedFilter.type.length > 0) {
            const typeIndex = params.length + 1
            postWhereConditions.push(
                `"ja"."type"::text = ANY($${typeIndex}::text[])`
            )
            params.push(combinedFilter.type)
        }

        if (postWhereConditions.length > 0) {
            subQuery += ` WHERE ${postWhereConditions.join(' AND ')}`
        }

        subQuery += ` GROUP BY "ja"."id" ORDER BY RANDOM() LIMIT 50`

        const query = subQuery

        const results = await this.prisma.$queryRawUnsafe(query, ...params)

        // @ts-expect-error ignore error here
        return results.map((result) => {
            const categories = result.categories.map((category) => category)
            return {
                ...result,
                id: result['jobAdvertId'],
                company: {
                    id: result['company_id'],
                    name: result['company_name'],
                    headerImageUrl: result['company_headerImageUrl'],
                    logoImageUrl: result['company_logoImageUrl'],
                    detailContent: result['company_detailContent'],
                    foundingYear: result['company_foundingYear'],
                    totalEmployees: result['company_totalEmployees'],
                    city: result['company_city'],
                    address: result['company_address'],
                },
                categories: categories,
            }
        })
    }

    async findNearbyJobAdverts(
        userLat: number = 49.9475,
        userLon: number = 11.5754,
        maxDistance: number = 47.8
    ) {
        //Testing with default:  Accra, Ghana
        const maxDistanceInMeters = maxDistance * 1000

        // Bayreuth, Germany Distance: 0 km Latitude: 49.9475° N Longitude: 11.5754° E
        // Hof, Germany Distance: 30.5 km Latitude: 50.3167° N Longitude: 11.9167° E

        // const distanceBetween = getDistance(
        //     { latitude: userLat, longitude: userLon }, //JobAdvert Location
        //     { latitude: 50.3167, longitude: 11.9167 } // Applicant Location
        // )

        return this.prisma.$queryRaw`
            SELECT *
            FROM "JobAdvert"
            WHERE ST_DWithin(
                    ST_MakePoint(longitude, latitude)::geography,
                    ST_MakePoint(${userLon}, ${userLat})::geography,
                    ${maxDistanceInMeters}
                      )`
    }

    jobAdvertsUnseen(applicantId: string) {
        return this.prisma.jobAdvert.findMany({
            where: {
                jobAction: {
                    none: {
                        applicantId: applicantId,
                    },
                },
            },
            include: {
                company: true,
                responsibleUsers: true,
            },
        })
    }
    jobAdvertsSeen(applicantId: string) {
        return this.prisma.jobAdvert.findMany({
            where: {
                jobAction: {
                    every: {
                        applicantId: applicantId,
                    },
                },
            },
            include: {
                company: true,
                responsibleUsers: true,
            },
        })
    }
    jobAdvertsBookmarked(applicantId: string) {
        return this.prisma.jobAdvert.findMany({
            where: {
                jobAction: {
                    every: {
                        applicantId: applicantId,
                        state: 'BOOKMARKED',
                        deletedFromCompany: false,
                    },
                },
            },
            include: {
                company: true,
                responsibleUsers: true,
            },
        })
    }

    jobAdvertsNotBookmarked(applicantId: string) {
        return this.prisma.jobAdvert.findMany({
            where: {
                jobAction: {
                    none: {
                        applicantId: applicantId,
                        state: 'BOOKMARKED',
                        deletedFromCompany: false,
                    },
                },
            },
            include: {
                company: true,
                responsibleUsers: true,
            },
        })
    }

    async findOne(
        id: string,
        superUserId?: string,
        companyUserId?: string
    ): Promise<JobAdvert> {
        const canAccess = await this.canAccessJobAdvert(id, companyUserId)
        if (!canAccess && !superUserId) {
            throw new NotFoundException(
                'Sorry you do not have permission to access this jobAdvert'
            )
        }

        try {
            return this.prisma.jobAdvert.findUnique({
                where: {
                    id: id,
                },
                include: {
                    subscriptions: true,
                    company: true,
                    categories: true,
                    responsibleUsers: true,
                    jobAction: true,
                },
            })
        } catch (error) {
            throw new NotFoundException(error.message)
        }
    }
    async findOneForApplicant(id: string): Promise<JobAdvert> {
        try {
            return this.prisma.jobAdvert.findUnique({
                where: {
                    id: id,
                    approved: true,
                    isDeclined: false,
                },
                include: {
                    subscriptions: true,
                    company: true,
                    categories: true,
                    responsibleUsers: true,
                    jobAction: true,
                },
            })
        } catch (error) {
            throw new NotFoundException(error.message)
        }
    }

    async jobAdvertStatsByCompany(id: string): Promise<JobAdvertStats> {
        try {
            const likesCount = await this.prisma.jobAction.count({
                where: {
                    jobAdvert: {
                        companyId: id,
                    },
                    state: 'LIKED',
                    deletedFromCompany: false,
                },
            })

            const bookmarkCount = await this.prisma.jobAction.count({
                where: {
                    jobAdvert: {
                        companyId: id,
                    },
                    state: 'BOOKMARKED',
                    deletedFromCompany: false,
                },
            })

            const viewsCount = await this.prisma.jobAction.count({
                where: {
                    jobAdvert: {
                        companyId: id,
                    },
                    deletedFromCompany: false,
                },
            })

            const matchCount = await this.prisma.jobAction.count({
                where: {
                    jobAdvert: {
                        companyId: id,
                    },
                    state: 'MATCHED',
                    deletedFromCompany: false,
                },
            })

            return {
                likes: likesCount,
                impressions: viewsCount,
                bookmarks: bookmarkCount,
                matches: matchCount,
            }
        } catch (error) {
            throw new NotFoundException(error.message)
        }
    }

    async update(
        id: string,
        categoryIdsToDisconnect: string[],
        categoryIdsToConnect: string[],
        responsibleUsersIdsToConnect: string[],
        responsibleUsersIdsToDisconnect: string[],
        companyId: string,
        updateJobAdvertInput: UpdateJobAdvertInput
    ): Promise<JobAdvert> {
        const jobAdvertUpdated = await this.prisma.jobAdvert.update({
            where: {
                id: id,
            },
            data: {
                ...updateJobAdvertInput,
                approved: false,
                isDeclined: false,
                categories: {
                    disconnect: categoryIdsToDisconnect?.map((id) => ({ id })),
                    connect: categoryIdsToConnect?.map((id) => ({ id })),
                },
                responsibleUsers: {
                    disconnect: responsibleUsersIdsToDisconnect?.map((id) => ({
                        id,
                    })),
                    connect: responsibleUsersIdsToConnect?.map((id) => ({
                        id,
                    })),
                },
                company: {
                    connect: {
                        id: companyId,
                    },
                },
            },
            include: this.jobAdvertInclude,
        })

        await this.publishJobAdvertEvent(jobAdvertUpdated)

        return jobAdvertUpdated
    }

    async approveJobAd(id: string, superUserId: string): Promise<JobAdvert> {
        const superUser = await this.prisma.superUser.findUnique({
            where: {
                id: superUserId,
            },
        })

        if (!superUser) {
            throw new Error('Super User not found')
        }

        const approvedJob = await this.prisma.jobAdvert.update({
            where: {
                id: id,
            },
            data: {
                approved: true,
                declineReason: null,
                isDeclined: false,
            },
            include: { jobAction: true },
        })

        await this.publishJobAdvertEvent(approvedJob)

        return approvedJob
    }

    async blockJobAd(
        id: string,
        superUserId: string,
        declineReason: string
    ): Promise<JobAdvert> {
        const superUser = await this.prisma.superUser.findUnique({
            where: {
                id: superUserId,
            },
        })

        if (!superUser) {
            throw new Error('Super User not found')
        }

        const blockedJobAd = await this.prisma.jobAdvert.update({
            where: {
                id: id,
            },
            data: {
                approved: false,
                declineReason: declineReason,
                isDeclined: true,
            },
            include: { jobAction: true },
        })

        await this.publishJobAdvertEvent(blockedJobAd)

        return blockedJobAd
    }

    async pauseResumeJob(id: string, paused: boolean) {
        const advertToSetPause = this.prisma.jobAdvert.findUnique({
            where: { id: id },
        })
        if (!advertToSetPause) {
            throw new NotFoundException(
                `Job Advert with ID ${id} does not exist`
            )
        }
        const jobAdvert = await this.prisma.jobAdvert.update({
            where: { id: id },
            data: { paused: paused },
            include: { jobAction: true, subscriptions: true },
        })

        await this.publishJobAdvertEvent(jobAdvert)

        return jobAdvert
    }
    async remove(id: string) {
        const advertToDelete = this.prisma.jobAdvert.findUnique({
            where: { id: id },
        })
        if (!advertToDelete) {
            throw new NotFoundException(
                `Job Advert with ID ${id} does not exist`
            )
        }

        await this.prisma.jobAction.deleteMany({
            where: { jobAdvertId: id },
        })

        return this.prisma.jobAdvert.delete({
            where: { id: id },
        })
    }

    async publishJobAdvertEvent(jobAdvert: any) {
        try {
            const channel = `liveCompanyJobAdverts.${jobAdvert.companyId}`

            const slimJobAdvert = {
                id: jobAdvert.id,
                title: jobAdvert.title,
                paused: jobAdvert.paused,
                jobAction: jobAdvert.jobAction,
                subscriptions: jobAdvert.subscriptions,
                isDeclined: jobAdvert.isDeclined,
                isDraft: jobAdvert.isDraft,
                approved: jobAdvert.approved,
            }

            await this.pusherService.publish(
                `private-${channel}`,
                'jobAdvertUpdated',
                slimJobAdvert
            )
        } catch (err) {
            console.log('pubSub error', err)
        } finally {
            console.log('pubSub finally')
        }
    }

    async canAccessJobAdvert(jobAdId: string, companyId: string) {
        const jobAdvert = await this.prisma.jobAdvert.findUnique({
            where: {
                id: jobAdId,
            },
            include: {
                responsibleUsers: { select: { id: true } },
            },
        })

        if (!jobAdvert) {
            return false
        }

        const responsibleUsers = jobAdvert.responsibleUsers.map(
            (user) => user.id
        )

        return responsibleUsers.includes(companyId)
    }
}
